import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  ActivityIndicator,
  Alert,
  Linking,
  TouchableOpacity,
  Text,
  StyleSheet,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { useNavigation } from '@react-navigation/native';
import {
  ComponentStatus,
  FDialog,
  showDialog,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import { SafeAreaView } from 'react-native-safe-area-context';
import ScreenHeader from '../Screen/Layout/header';
import {
  navigate,
  navigateBack,
  navigateReset,
  RootScreen,
} from '../router/router';
import { AppDispatch } from '../redux/store/store';
import { useDispatch } from 'react-redux';
import { CustomerActions } from '../redux/reducers/CustomerReducer';
import { Sakupi, SakupiType, StatusOrder } from '../Config/Contanst';
import { DataController } from '../base/baseController';

const VnpayPaymentScreen = ({ route }: any) => {
  const { paymentUrl, id, OrderId } = route.params;
  const navigation = useNavigation<any>();
  const webViewRef = useRef(null);
  const [loading, setLoading] = useState(true);
  //distpatch
  const dispatch: AppDispatch = useDispatch();
  const [isSuccess, setIsSuccess] = useState(false);
  function getQueryParams(url: string): Record<string, string> {
    const params: Record<string, string> = {};
    const queryString = url.split('?')[1] || '';
    const pairs = queryString.split('&');

    for (const pair of pairs) {
      const [key, value] = pair.split('=');
      if (key) {
        params[decodeURIComponent(key)] = decodeURIComponent(value || '');
      }
    }

    return params;
  }

  const dialogRef = useRef<any>(null);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <ScreenHeader
        title="Thanh toán VNPAY"
        onBack={() => navigation.goBack()}
        backIcon={<Winicon src="fill/user interface/e-remove" size={20} />}
      />
      <FDialog ref={dialogRef} />
      {/* WebView */}
      {isSuccess ? (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text>Thanh toán thành công</Text>
        </View>
      ) : (
        <WebView
          ref={webViewRef}
          source={{ uri: paymentUrl }}
          onLoadStart={() => setLoading(true)}
          onNavigationStateChange={async (navState: any) => {
            const myParam = getQueryParams(navState.url);
            if (navState.url.startsWith('https://eschool.wini.vn')) {
              //lấy order
              const ordercontroller = new DataController('OrderDetail');
              const order = await ordercontroller.getById(OrderId);
              debugger
              if (order?.code !== 200) {
                showDialog({
                  ref: dialogRef,
                  status: ComponentStatus.ERROR,
                  title: 'Thanh toán thất bại',
                  content:
                    'Thanh toán khoá học không thành công, vui lòng kiểm tra lại?',
                  titleSubmit: 'Xem khoá học',
                  onSubmit: () => {
                    navigateBack();
                  },
                });
              } else {
                if (myParam.vnp_ResponseCode === '00' && order?.data?.Status === StatusOrder.success) {
                  setIsSuccess(true);
                  dispatch(CustomerActions.updateRank(SakupiType.buyCourse, Sakupi.buyCourse));
                  showDialog({
                    ref: dialogRef,
                    status: ComponentStatus.SUCCSESS,
                    title: 'Thanh toán thành công',
                    content:
                      'Bây giờ bạn có thể tham gia khoá học này. Bạn muốn xem khoá học hay không?',
                    onCancel: () => {
                      navigateReset(RootScreen.navigateESchoolView);
                    },
                    titleCancel: 'Quay lại',
                    titleSubmit: 'Xem khoá học',
                    onSubmit: () => {
                      // navigateReset(RootScreen.ProccessCourse, {id: id});
                      navigateBack();
                      navigateBack();
                      navigation.replace(RootScreen.CourseDetail, { id: id });
                    },
                  });
                  //show giao diện thông báo thành công
                } else {
                  setIsSuccess(false);

                  showDialog({
                    ref: dialogRef,
                    status: ComponentStatus.ERROR,
                    title: 'Thanh toán thất bại',
                    content:
                      'Thanh toán khoá học không thành công, vui lòng kiểm tra lại?',
                    titleSubmit: 'Xem khoá học',
                    onSubmit: () => {
                      navigateBack();
                    },
                  });
                }
              }

              // Alert.alert('Thanh toán thành công', 'Cảm ơn bạn đã thanh toán.');
            }
          }}
          onLoadEnd={() => setLoading(false)}
          startInLoadingState
        />
      )}
      {/* Loading Overlay */}
      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#fff" />
          <Text style={styles.loadingText}>
            {isSuccess
              ? 'Thanh toán thành công'
              : 'Đang tải trang thanh toán...'}
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

export default VnpayPaymentScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    height: 56,
    backgroundColor: '#f5f5f5',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
  },
  backButton: {
    width: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    color: '#fff',
    fontWeight: '600',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 56, // dưới header
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 14,
  },
});
