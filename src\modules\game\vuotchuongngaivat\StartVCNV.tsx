import {
  ActivityIndicator,
  Alert,
  Keyboard,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Vibration,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {MiniQuestion, Word} from './models/models';
import React, {useEffect, useRef, useState} from 'react';
import HeadGame from '../components/HeadGame';
import Lives from '../components/Lives';
import {BottomGame} from '../components/BottomGame';
import {useVcnvHook} from './redux/hooks/vcnvHook';
import {useSelector} from 'react-redux';
import store, {RootState} from '../../../redux/store/store';
import {checkPositionOrder, replaceObjectById} from '../utils/functions';
import {useGameHook} from '../../../redux/hook/gameHook';
import GameOverModal from '../components/GameOverModel';
import HintModel from '../components/HintModel';
import ModelConfirm from '../components/ModelConfirm';
import ModelPauseGame from '../components/ModelPauseGame';
import {useNavigation, useRoute} from '@react-navigation/native';
import ConfigAPI from '../../../Config/ConfigAPI';
import WinnerModal from './components/WinnerModal';
import {GameDA} from '../gameDA';
import {DataController} from '../../../base/baseController';
import {randomGID} from '../../../utils/Utils';
import Sound from 'react-native-sound';
import {shuffleArray} from '../../../utils/arrayUtils';

interface DropZone {
  id: string;
  miniQuestionId: string;
  word?: Word | null;
  index: number;
  isShow?: boolean;
}
// DraggableWord component moved outside to avoid recreation on every render
const DraggableWord = React.memo(
  ({word, onDrop}: {word: Word; onDrop: (word: Word) => void}) => {
    return (
      <TouchableOpacity
        style={[styles.wordContainer]}
        onPress={() => onDrop(word)}>
        <View style={styles.draggableWordInnerView}>
          <Text style={styles.wordText}>{word.text}</Text>
        </View>
      </TouchableOpacity>
    );
  },
);
const StartVCNV = () => {
  const vcnvHook = useVcnvHook();
  const gameHook = useGameHook();
  const {
    listMiniQuestion,
    currentQuestion,
    gameConfig,
    loading,
    configLoading,
    error,
    configError,
    usedHints,
    crosswordData,
  } = useSelector((state: RootState) => state.VTNVStore);
  const {currentLives, totalLives, gem, gemUse, isGameOver, messageGameOver} =
    useSelector((state: RootState) => state.gameStore);
  const route = useRoute<any>();
  const gameId = ConfigAPI.gameVTNV;
  const milestoneId = route.params?.milestoneId || 1;
  const competenceId = route.params?.competenceId || '';

  const [listWord, setListWord] = useState<Word[]>([]);
  const [listDropZone, setListDropZone] = useState<DropZone[]>([]);
  const [currentMiniQuestion, setCurrentMiniQuestion] =
    useState<MiniQuestion | null>(null);
  const [answer, setAnswer] = useState<string>('');
  const [isError, setIsError] = useState<boolean>(false);
  const [isCorrect, setIsCorrect] = useState<boolean>(false);
  const [statusMiniQuestion, setStatusMiniQuestion] = useState<
    'correct' | 'wrong' | null
  >(null);
  const [isShowModelConfirm, setIsShowModelConfirm] = useState<boolean>(false);
  const [isShowHintModel, setIsShowHintModel] = useState<boolean>(false);
  const [isPauseGame, setIsPauseGame] = useState<boolean>(false);
  const [isShowKeyboard, setIsShowKeyboard] = useState<boolean>(false);
  const navigation = useNavigation();
  const [listMiniQuestionDone, setListMiniQuestionDone] = useState<
    MiniQuestion[]
  >([]);

  const refDropZone = useRef<{[key: string]: View | null}>({});
  const hiddenInputRef = useRef<TextInput | null>(null);
  const [isWinLevel, setIsWinLevel] = useState<boolean>(false);

  // New scoring tracking states
  const [completedHorizontalRows, setCompletedHorizontalRows] =
    useState<number>(0);
  const [keywordAnsweredEarly, setKeywordAnsweredEarly] =
    useState<boolean>(false);
  const [allHorizontalRowsCompleted, setAllHorizontalRowsCompleted] =
    useState<boolean>(false);

  // Audio states
  const [audioPlayer, setAudioPlayer] = useState<Sound | null>(null);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);

  // Add timeout ref to track and clear timeouts
  const resetTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Kiểm tra hint dựa trên câu hỏi từ khóa
  const keywordQuestionText = crosswordData?.keywordQuestion?.hint;
  const hasHint = keywordQuestionText && keywordQuestionText.trim() !== '';
  const keywordQuestionId = crosswordData?.keywordQuestion?.id;
  const isHintUsed = usedHints.includes(keywordQuestionId?.toString() || '');
  const shouldShowHintButton = Boolean(hasHint && !isHintUsed);
  // Đặt resetState lên trước để tránh lỗi dùng trước khi khai báo
  const resetState = React.useCallback(() => {
    setListWord([]);
    setListDropZone([]);
    setCurrentMiniQuestion(null);
    setIsCorrect(false);
    setIsError(false);
    setStatusMiniQuestion(null);
    setIsShowKeyboard(false);
    // Reset scoring tracking states
    setCompletedHorizontalRows(0);
    setKeywordAnsweredEarly(false);
    setAllHorizontalRowsCompleted(false);
    setListMiniQuestionDone([]);
    refDropZone.current = {};
    hiddenInputRef.current?.blur();
  }, []);

  // Sử dụng ref để lưu gameConfig mới nhất, tránh vòng lặp useCallback
  const gameConfigRef = React.useRef(gameConfig);
  useEffect(() => {
    gameConfigRef.current = gameConfig;
  }, [gameConfig]);

  // Đảm bảo initializeGame có dependency đúng, không gây lặp vô hạn
  const initializeGame = React.useCallback(async () => {
    try {
      resetState();
      gameHook.restartGame();
      const result = await vcnvHook.initializeGame({
        gameId,
        milestoneId,
        competenceId,
      });
      const latestGameConfig = gameConfigRef.current;
      if (result.success && latestGameConfig) {
        gameHook.setData({
          stateName: 'totalLives',
          value: latestGameConfig.maxLives,
        });
        gameHook.setData({
          stateName: 'currentLives',
          value: latestGameConfig.maxLives,
        });
        gameHook.setData({
          stateName: 'gemAdd',
          value: latestGameConfig.scorePerLife,
        });
        gameHook.setData({
          stateName: 'gemCost',
          value: latestGameConfig.gemHint,
        });
        gameHook.setData({
          stateName: 'time',
          value: latestGameConfig.timeLimit,
        });
        gameHook.setData({stateName: 'isRunTime', value: true});
      } else {
        vcnvHook.startGame();
      }
    } catch (err) {
      vcnvHook.startGame();
    }
  }, [gameId, milestoneId, competenceId]);

  useEffect(() => {
    initializeGame();
    fetchScore();
    Keyboard.addListener('keyboardDidShow', () => {
      setIsShowKeyboard(true);
    });
    Keyboard.addListener('keyboardDidHide', () => {
      setIsShowKeyboard(false);
      hiddenInputRef.current?.blur();
    });
    return () => {
      // Clear any pending timeout when component unmounts
      if (resetTimeoutRef.current) {
        clearTimeout(resetTimeoutRef.current);
        resetTimeoutRef.current = null;
      }
      refDropZone.current = {};
      Keyboard.removeAllListeners('keyboardDidShow');
      Keyboard.removeAllListeners('keyboardDidHide');
    };
  }, []); // chỉ chạy 1 lần khi mount
  const fetchScore = async () => {
    try {
      // Lấy thông tin điểm từ bảng GameCUstomer
      const gameDa = new GameDA();
      const result = await gameDa.getScoreByCustomerIdAndGameId(
        store.getState().customer.data.Id,
        ConfigAPI.gameVTNV,
      );
      gameHook.setData({stateName: 'gem', value: result ?? 0});
    } catch (error) {
      console.error('Lỗi khi lấy thông tin điểm:', error);
    }
  };
  useEffect(() => {
    if (currentLives < 1) gameOver('Thất bại rồi, làm lại nào');
  }, [currentLives]);

  useEffect(() => {
    if (listMiniQuestionDone.length === listMiniQuestion?.length) {
      // All horizontal rows completed
      setAllHorizontalRowsCompleted(true);
      setCompletedHorizontalRows(listMiniQuestionDone.length);
      // Only set win level if keyword hasn't been answered yet
      // If keyword was answered early, the game should already be won
      if (!keywordAnsweredEarly) {
        // Don't auto-win, let player answer the keyword
        console.log(
          '[VCNV] All horizontal rows completed, waiting for keyword answer',
        );
      }
    }
  }, [listMiniQuestionDone, listMiniQuestion?.length, keywordAnsweredEarly]);

  const startGame = () => {
    // Clear any pending timeout before restarting
    if (resetTimeoutRef.current) {
      clearTimeout(resetTimeoutRef.current);
      resetTimeoutRef.current = null;
    }

    resetState();
    vcnvHook.startGameWithAPI();

    // Apply gameConfig if available, otherwise use default values
    if (gameConfig) {
      gameHook.setData({stateName: 'totalLives', value: gameConfig.maxLives});
      gameHook.setData({stateName: 'currentLives', value: gameConfig.maxLives});
      gameHook.setData({stateName: 'time', value: gameConfig.timeLimit});
      gameHook.setData({stateName: 'isRunTime', value: true});
      gameHook.setData({stateName: 'isGameOver', value: false});
      gameHook.setData({stateName: 'gemAdd', value: gameConfig.scorePerLife});
      gameHook.setData({stateName: 'gemCost', value: gameConfig.gemHint});
      console.log(
        `[VCNV] Game restarted with config: ${gameConfig.maxLives} lives, ${gameConfig.timeLimit}s timer`,
      );
    } else {
      gameHook.restartGame();
    }
  };

  const gameOver = React.useCallback(
    (message: string) => {
      gameHook.gameOver(message);
    },
    [gameHook],
  );

  // Tạm dừng game
  const onPauseGame = () => {
    gameHook.pauseGame();
    setIsPauseGame(true);
  };

  // Tiếp tục game
  const onContinueGame = () => {
    gameHook.continueGame();
    setIsPauseGame(false);
  };

  // Hiển thị bàn phím
  const showKeyboard = () => {
    // Clear any pending reset timeout
    if (resetTimeoutRef.current) {
      clearTimeout(resetTimeoutRef.current);
      resetTimeoutRef.current = null;
    }

    setIsError(false);
    setIsCorrect(false);
    setAnswer('');
    if (hiddenInputRef.current) {
      hiddenInputRef.current.focus();
      refDropZone.current = {};
      setCurrentMiniQuestion(null);
      setTimeout(() => {
        setListWord([]);
      }, 200);
    }
  };
  const useHint = () => {
    if (gem < (gameConfig?.gemHint || 0)) {
      // show model thông báo không đủ gem
      Alert.alert(
        'Thông báo',
        'Bạn không đủ gem để sử dụng gợi ý',
        [
          {
            text: 'OK',
            style: 'cancel',
          },
        ],
        {cancelable: false},
      );
      return;
    }
    gameHook.setData({
      stateName: 'gem',
      value: gem - (gameConfig?.gemHint || 0),
    });
    setIsShowModelConfirm(false);
    updateScore(gameConfig?.gemHint || 0);

    // Mark hint đã được sử dụng cho câu hỏi từ khóa
    if (keywordQuestionId) {
      vcnvHook.markHintUsed(keywordQuestionId);
    }
  };
  const updateScore = async (score: number) => {
    if (score <= 0) return;
    const gamecustomerController = new DataController('GameCustomer');
    const customerId = store.getState().customer.data.Id;
    const game = {
      Id: randomGID(),
      CustomerId: customerId,
      GameId: ConfigAPI.gameVTNV,
      Stage: milestoneId,
      Competency: competenceId,
      Status: 0,
      DateCreated: new Date().getTime(),
      Score: -(gameConfig?.gemHint || 0),
      HighestScore: 0,
      PlayedAt: new Date().getTime(),
      Name: `Sử dụng gợi ý - VCNC_${milestoneId}`,
    };
    console.log('Tạo bản ghi mới:', game);
    const result = await gamecustomerController.add([game]);
    if (result.code !== 200) {
      return false;
    }
    setIsShowHintModel(true);
    return true;
  };

  // Setup audio when current question or mini question changes
  useEffect(() => {
    // Ưu tiên audio của mini question nếu có, nếu không thì dùng audio của question chính
    const audioUrl = currentMiniQuestion?.audioUrl || currentQuestion?.audioUrl;

    if (audioUrl) {
      setupAudio(audioUrl);
    } else {
      // Cleanup audio nếu không có audio URL
      if (audioPlayer) {
        audioPlayer.stop();
        audioPlayer.release();
        setAudioPlayer(null);
        setIsPlayingAudio(false);
      }
    }

    // Cleanup previous audio
    return () => {
      if (audioPlayer) {
        audioPlayer.stop();
        audioPlayer.release();
      }
    };
  }, [currentQuestion, currentMiniQuestion]);

  // Setup audio player
  const setupAudio = (audioUrl: string) => {
    console.log('===== SETTING UP AUDIO =====');
    console.log('Audio URL:', audioUrl);

    // Release previous audio
    if (audioPlayer) {
      audioPlayer.stop();
      audioPlayer.release();
    }

    // Enable playback in silence mode (iOS)
    Sound.setCategory('Playback');

    // Create new audio instance
    const sound = new Sound(audioUrl, '', audioError => {
      if (audioError) {
        console.error('Failed to load audio:', audioError);
        return;
      }
      console.log('Audio loaded successfully');
      setAudioPlayer(sound);
    });
  };

  // Play audio function
  const playAudio = () => {
    console.log('===== PLAY AUDIO CALLED =====');
    console.log('Audio player:', audioPlayer);
    console.log('Is playing:', isPlayingAudio);

    if (!audioPlayer) {
      console.log('No audio player available');
      return;
    }

    if (isPlayingAudio) {
      // Stop audio if currently playing
      audioPlayer.stop(() => {
        console.log('Audio stopped');
        setIsPlayingAudio(false);
      });
    } else {
      // Play audio
      setIsPlayingAudio(true);
      audioPlayer.play(success => {
        console.log('Audio play finished, success:', success);
        setIsPlayingAudio(false);
      });
    }
  };

  // Reset mini question - đặt lại dropzone.word = null và reset listWord
  const resetMiniQuestion = React.useCallback(() => {
    if (!currentMiniQuestion?.id) return;
    // Lấy tất cả các word từ các dropzone của mini question hiện tại
    const newListWord: Word[] = listDropZone
      .filter(
        zone =>
          zone.miniQuestionId === currentMiniQuestion.id &&
          zone.word &&
          zone.isShow,
      )
      .map(zone => zone.word as Word);
    // Reset listWord về trạng thái ban đầu
    setListWord(newListWord);

    // Đặt tất cả dropzone.word = null cho mini question hiện tại
    const updatedListDropZone = listDropZone.map(zone =>
      zone.miniQuestionId === currentMiniQuestion.id && zone.word && zone.isShow
        ? {...zone, word: null}
        : zone,
    );
    setListDropZone(updatedListDropZone);

    // Cập nhật lại listMiniQuestion với listWord mới
    const updatedListMiniQuestion = replaceObjectById(
      {...currentMiniQuestion, listWord: newListWord},
      listMiniQuestion,
    );
    vcnvHook.setData({
      stateName: 'listMiniQuestion',
      value: updatedListMiniQuestion,
    });
  }, [currentMiniQuestion, listDropZone, listMiniQuestion, vcnvHook]);

  // Đáp án sai
  const onWrongAnswer = React.useCallback(
    (type: 'normal' | 'key') => {
      // Rung thiết bị khi trả lời sai
      Vibration.vibrate([0, 500, 200, 500]);

      if (type === 'normal') {
        setStatusMiniQuestion('wrong');

        // Clear any existing timeout
        if (resetTimeoutRef.current) {
          clearTimeout(resetTimeoutRef.current);
        }

        // Set new timeout and store reference
        resetTimeoutRef.current = setTimeout(() => {
          setStatusMiniQuestion(null);
          resetMiniQuestion();
          resetTimeoutRef.current = null;
        }, 1000);
      } else {
        setIsError(true);
      }
      gameHook.setData({stateName: 'currentLives', value: currentLives - 1});
    },
    [currentLives, resetMiniQuestion, currentMiniQuestion, gameHook],
  );

  // Đáp án đúng
  const onCorrectAnswer = React.useCallback(
    (type: 'normal' | 'key') => {
      if (type === 'normal') {
        setStatusMiniQuestion('correct');
        setListMiniQuestionDone(prev => {
          const newList = [...prev, currentMiniQuestion as MiniQuestion];
          // Update completed horizontal rows count
          setCompletedHorizontalRows(newList.length);
          return newList;
        });
        setTimeout(() => {
          setStatusMiniQuestion(null);
        }, 2000);
      } else {
        // Keyword answered correctly
        setIsCorrect(true);

        // Check if this is an early keyword answer (not all horizontal rows completed)
        const totalHorizontalRows = listMiniQuestion?.length || 0;
        const currentCompletedRows = listMiniQuestionDone.length;

        if (currentCompletedRows < totalHorizontalRows) {
          // Keyword answered early - eligible for bonus
          setKeywordAnsweredEarly(true);
          console.log('[VCNV] Keyword answered early! Bonus eligible.');
        } else {
          // All horizontal rows completed first - no bonus
          console.log('[VCNV] All horizontal rows completed first. No bonus.');
        }

        // Set final completed count for scoring
        setCompletedHorizontalRows(currentCompletedRows);
        setIsWinLevel(true);

        setTimeout(() => {
          setIsCorrect(false);
        }, 2000);
      }
    },
    [
      currentMiniQuestion,
      listMiniQuestionDone.length,
      listMiniQuestion?.length,
    ],
  );

  // Kiểm tra kết quả của câu hàng ngang
  const checkAnswerRow = React.useCallback(() => {
    const zoneOfCurrentMiniQuestion = listDropZone.filter(
      zone =>
        zone.miniQuestionId === currentMiniQuestion?.id &&
        zone.word &&
        zone.isShow,
    );
    zoneOfCurrentMiniQuestion.sort((a, b) => a.index - b.index);

    const wordsInOrder = zoneOfCurrentMiniQuestion.map(zone => zone.word);

    const isAnswerCorrect = checkPositionOrder(wordsInOrder);
    if (isAnswerCorrect) {
      onCorrectAnswer('normal');
    } else {
      onWrongAnswer('normal');
    }
  }, [listDropZone, currentMiniQuestion, onCorrectAnswer, onWrongAnswer]);

  // Khi chọn hàng ngang
  const chooseRow = (miniQuestion: MiniQuestion) => {
    refDropZone.current = {};
    setCurrentMiniQuestion(miniQuestion);
    setTimeout(() => {
      const listWords = shuffleArray(miniQuestion.listWord);
      setListWord(listWords);
    }, 200);
  };

  // Thả word vào dropzone
  const addWordToDropZone = React.useCallback(
    (word: Word) => {
      // Thêm word vào dropZone
      const dropZone = listDropZone.find(
        zone =>
          zone.miniQuestionId === currentMiniQuestion?.id &&
          zone.word === null &&
          zone.isShow,
      );
      if (dropZone) {
        if (dropZone.word) return;
        dropZone.word = word;
        const newListDropZone = replaceObjectById(dropZone, listDropZone);
        setListDropZone(newListDropZone);

        // Xoá word khỏi listWord
        const newListWord = listWord.filter(w => w.id !== word.id);
        setListWord(newListWord);

        // Sửa lại listMiniQuestion
        const cloneCurrentMiniQuestion = {...currentMiniQuestion};
        if (cloneCurrentMiniQuestion) {
          cloneCurrentMiniQuestion.listWord = [...newListWord];
          const newListMiniQuestion = replaceObjectById(
            cloneCurrentMiniQuestion,
            listMiniQuestion,
          );
          vcnvHook.setData({
            stateName: 'listMiniQuestion',
            value: newListMiniQuestion,
          });
        }

        // nếu listWord rỗng thì check answer
        if (newListWord.length === 0) {
          checkAnswerRow();
        }
      }
    },
    [
      listDropZone,
      listWord,
      currentMiniQuestion,
      listMiniQuestion,
      vcnvHook,
      checkAnswerRow,
    ],
  );

  // CLick vào dropzone có chứa text
  const clickDropZoneWord = (dropZone: DropZone) => {
    // Kiểm tra dropzone có được hiển thị không
    if (!dropZone.isShow) {
      console.log(`[VCNV] Cannot click hidden dropzone ${dropZone.id}`);
      return;
    }

    const newListWord = [...listWord];
    if (newListWord) {
      newListWord.push(dropZone.word as Word);
    }
    // Thêm word vào listWord
    setListWord(newListWord);

    // // Sửa lại listMiniQuestion
    const cloneCurrentMiniQuestion = {...currentMiniQuestion};
    cloneCurrentMiniQuestion.listWord = newListWord;
    const newListMiniQuestion = replaceObjectById(
      cloneCurrentMiniQuestion,
      listMiniQuestion,
    );
    vcnvHook.setData({
      stateName: 'listMiniQuestion',
      value: newListMiniQuestion,
    });

    dropZone.word = null;
    const newListDropZone = replaceObjectById(dropZone, listDropZone);
    setListDropZone(newListDropZone);
  };

  // Kiểm tra đáp án hàng dọc key
  const checkAnswer = () => {
    setIsError(false);
    setIsCorrect(false);
    if (answer.toLowerCase() === currentQuestion?.answer) {
      onCorrectAnswer('key');
    } else {
      onWrongAnswer('key');
    }
  };

  // Key column cố định là cột thứ 5 (index 4 trong 0-based, index 5 trong 1-based)
  const optimalKeyColumn = 4;

  // Render drop zone
  const renderDropZone = (index: number, miniQuestion: MiniQuestion) => {
    const totalWords = miniQuestion.length;
    // Convert to 0-based để khớp với logic trong vcnvDA.ts
    const keyWordIndex = miniQuestion.keyIndex - 1; // Convert to 0-based
    const keyColumnIndex = 3; // Key column cố định ở index 4 (0-based)

    // Tính toán giống như trong vcnvDA.ts
    const wordStartCol = keyColumnIndex - keyWordIndex; // 0-based
    const wordEndCol = wordStartCol + totalWords - 1; // 0-based

    let startColumn: number; // 1-based for UI
    let endColumn: number; // 1-based for UI
    let actualKeyColumn: number; // 1-based for UI
    let canAlign = false;

    // Kiểm tra có thể align với key column cố định không
    if (wordStartCol >= 0 && wordEndCol < 9) {
      // Có thể align với key column cố định (cột thứ 5)
      startColumn = wordStartCol + 1; // Convert to 1-based
      endColumn = wordEndCol + 1; // Convert to 1-based
      actualKeyColumn = optimalKeyColumn; // Cột thứ 5
      canAlign = true;
    } else {
      // Không thể align, đặt ở vị trí tối ưu khác
      const totalColumns = 9;
      const leftPadding = Math.floor((totalColumns - totalWords) / 2);
      startColumn = leftPadding + 1;
      endColumn = startColumn + totalWords - 1;
      actualKeyColumn = startColumn + keyWordIndex; // keyWordIndex is 0-based, startColumn is 1-based
      canAlign = false;
    }

    // Debug log
    if (index === 1) {
      console.log(
        `[VCNV Debug] Question ${
          miniQuestion.id
        }: totalWords=${totalWords}, keyIndex=${
          keyWordIndex + 1
        }, startColumn=${startColumn}, endColumn=${endColumn}, actualKeyColumn=${actualKeyColumn}, canAlign=${canAlign}, optimalKeyColumn=${optimalKeyColumn}`,
      );
    }

    // Kiểm tra ô này có được hiển thị không
    const isShow = index >= startColumn && index <= endColumn;

    // Kiểm tra ô này có phải là cột Key không
    const isKeyColumn = index === actualKeyColumn;

    const isChoose =
      currentMiniQuestion?.id === miniQuestion.id ||
      (isKeyColumn && isShowKeyboard);
    const dropZone = listDropZone.find(
      zone => zone.id === `${miniQuestion.id}${index}`,
    );
    if (!dropZone) {
      listDropZone.push({
        id: `${miniQuestion.id}${index}`,
        miniQuestionId: miniQuestion.id,
        word: null,
        index,
        isShow: isShow, // Lưu trạng thái hiển thị
      });
    } else {
      // Cập nhật isShow cho dropzone đã tồn tại
      dropZone.isShow = isShow;
    }

    // Determine background color
    const getBackgroundColor = () => {
      if (isKeyColumn) {
        return '#1BDB55';
      }
      return isChoose ? 'white' : '#E8E8E8';
    };

    return isShow ? (
      <TouchableOpacity
        onPress={() => {
          if (listMiniQuestionDone.find(i => i.id === miniQuestion.id)) return;
          if (isChoose && dropZone?.word) return clickDropZoneWord(dropZone);
          chooseRow(miniQuestion);
        }}
        ref={ref => {
          if (
            miniQuestion.id === currentMiniQuestion?.id &&
            refDropZone.current &&
            isShow // Chỉ thêm vào refDropZone nếu dropzone được hiển thị
          ) {
            refDropZone.current[`${miniQuestion.id}${index}`] = ref;
          }
        }}
        key={index}
        style={[
          styles.dropZone,
          {backgroundColor: getBackgroundColor()},
          isChoose ? styles.dropZoneChosen : null,
        ]}>
        <View style={styles.dropZoneInnerView}>
          <Text style={styles.wordText}>{dropZone?.word?.text || ''}</Text>
        </View>
      </TouchableOpacity>
    ) : (
      <View key={index} style={styles.dropZonePlaceholder}></View>
    );
  };

  // Show loading UI while data is being loaded
  if (loading || configLoading || !listMiniQuestion || !gameConfig) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1BDB55" />
          <Text style={styles.loadingText}>Đang tải dữ liệu game...</Text>
          {configLoading && (
            <Text style={styles.loadingSubText}>Đang tải cấu hình game</Text>
          )}
          {loading && (
            <Text style={styles.loadingSubText}>Đang tải câu hỏi</Text>
          )}
        </View>
      </SafeAreaView>
    );
  }

  // Show error UI if failed to load data
  if (error || configError) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Lỗi tải dữ liệu</Text>
          <Text style={styles.errorMessage}>
            {error || configError || 'Không thể tải dữ liệu game'}
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={initializeGame}>
            <Text style={styles.retryButtonText}>Thử lại</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.flex1}>
        {/* Header */}
        <View style={styles.headerContainer}>
          <HeadGame
            timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
            isShowSuggest={shouldShowHintButton}
            onUseHint={() => setIsShowModelConfirm(true)}
            gameId={ConfigAPI.gameVTNV}
          />
          <View style={styles.livesContainer}>
            <Lives totalLives={totalLives} currentLives={currentLives} />
          </View>
        </View>

        {/* Body */}
        {isPauseGame ? (
          <View style={styles.pauseContainer}>
            <ModelPauseGame
              visible={isPauseGame}
              message={'Bạn đang tạm dừng trò chơi'}
              onContinue={onContinueGame}
            />
          </View>
        ) : (
          <View style={styles.bodyContainer}>
            <View style={styles.miniQuestionStatusContainer}>
              {statusMiniQuestion === 'correct' && (
                <Text style={styles.correctText}>Đáp án chính xác</Text>
              )}
              {statusMiniQuestion === 'wrong' && (
                <Text style={styles.errorText}>
                  Sai rồi, hãy thử đáp án khác
                </Text>
              )}
            </View>
            {listMiniQuestion?.map(
              (miniQuestion: MiniQuestion, rowIndex: number) => {
                return (
                  <View style={styles.row} key={rowIndex}>
                    {Array.from({length: 9}).map((_, colIndex) => {
                      return renderDropZone(colIndex, miniQuestion);
                    })}
                  </View>
                );
              },
            )}
            <View style={styles.gameAreaContainer}>
              {!isShowKeyboard ? (
                <View style={styles.checkButtonContainer}>
                  <TouchableOpacity
                    style={[styles.checkButton, styles.solveKeywordButton]}
                    onPress={showKeyboard}>
                    <Text style={styles.checkButtonText}>Giải từ khoá</Text>
                  </TouchableOpacity>
                </View>
              ) : null}
              <View>
                <View style={styles.questionContainer}>
                  {/* Audio button - hiển thị khi có audio (ưu tiên mini question, fallback về question chính) */}
                  {(currentMiniQuestion?.audioUrl ||
                    (!currentMiniQuestion && currentQuestion?.audioUrl)) && (
                    <TouchableOpacity
                      style={styles.audioButton}
                      onPress={playAudio}>
                      <Text style={styles.audioIcon}>
                        {isPlayingAudio ? '⏸️' : '🔊'}
                      </Text>
                    </TouchableOpacity>
                  )}

                  {/* Question title */}
                  <View style={styles.questionTitleContainer}>
                    <Text style={styles.questionTitle}>
                      {!currentMiniQuestion
                        ? currentQuestion?.text || ''
                        : currentMiniQuestion?.text || ''}
                    </Text>
                  </View>
                </View>
              </View>
              <View style={styles.wordsContainer}>
                {listWord.map((word: Word, index) => (
                  <DraggableWord
                    key={index}
                    word={word}
                    onDrop={addWordToDropZone}
                  />
                ))}
              </View>
            </View>

            {isShowKeyboard ? (
              <View style={styles.keyboardViewContainer}>
                <View
                  style={[
                    styles.answerContainer,
                    isError ? styles.answerContainerError : null,
                    isCorrect ? styles.answerContainerCorrect : null,
                  ]}>
                  {answer.length < 1 ? (
                    <Text style={styles.placeholderText}>
                      Vui lòng nhập đáp án
                    </Text>
                  ) : (
                    <Text style={styles.answerText}>{answer}</Text>
                  )}
                  {isCorrect && (
                    <Text style={styles.correctText}>Đáp án chính xác</Text>
                  )}
                  {isError && (
                    <Text style={styles.errorText}>
                      Sai rồi, hãy thử đáp án khác
                    </Text>
                  )}
                </View>
                <View style={styles.checkAnswerButtonContainer}>
                  <View style={styles.spacer}></View>
                  <TouchableOpacity
                    style={[styles.checkButton, styles.checkAnswerButton]}
                    onPress={checkAnswer}>
                    <Text style={styles.checkButtonText}>Kiểm tra đáp án</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : null}
          </View>
        )}

        {/* Bottom */}
        <View style={styles.bottomContainer}>
          <BottomGame
            resetGame={startGame}
            backGame={() => {
              navigation.goBack();
            }}
            pauseGame={onPauseGame}
            volumeGame={() => {}}
          />
        </View>
        <TextInput
          ref={hiddenInputRef}
          style={styles.hiddenInput}
          value={answer}
          onChangeText={text => setAnswer(text)}
          autoCapitalize="none"
          autoCorrect={false}
          spellCheck={false}
          caretHidden={true}
        />
      </View>
      <View style={styles.modalContainer}>
        <ModelConfirm
          isShow={isShowModelConfirm}
          closeModal={() => setIsShowModelConfirm(false)}
          onConfirm={useHint}
          message={`Bạn sẽ bị trừ ${5} điểm khi sử dụng trợ giúp này`}
        />
        <HintModel
          isShow={isShowHintModel}
          closeModal={() => setIsShowHintModel(false)}
          text={
            crosswordData?.keywordQuestion?.hint || currentQuestion?.hint || ''
          }
        />
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={startGame}
          message={messageGameOver}
          isTimeOut={false}
        />
        <WinnerModal
          visible={isWinLevel}
          onClose={() => {
            setIsWinLevel(false);
          }}
          restartGame={startGame}
          currentLives={currentLives}
          competenceId={competenceId}
          gameId={ConfigAPI.gameVTNV}
          completedHorizontalRows={completedHorizontalRows}
          keywordAnsweredEarly={keywordAnsweredEarly}
          totalHorizontalRows={listMiniQuestion?.length || 0}
        />
      </View>
    </SafeAreaView>
  );
};
export default StartVCNV;

const styles = StyleSheet.create({
  draggableWordInnerView: {
    height: 36,
    width: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#FFC670',
  },
  flex1: {
    flex: 1,
  },
  headerContainer: {
    marginHorizontal: 16,
  },
  livesContainer: {
    marginTop: 8,
  },
  pauseContainer: {
    zIndex: 1000,
  },
  bodyContainer: {
    flex: 1,
    marginHorizontal: 12,
    borderRadius: 16,
  },
  miniQuestionStatusContainer: {
    marginVertical: 6,
  },
  row: {
    flexDirection: 'row',
  },
  dropZone: {
    width: 36,
    height: 36,
    margin: 1,
    borderRadius: 4,
  },
  dropZoneChosen: {
    borderWidth: 1,
    borderColor: '#AE2B26',
  },
  dropZoneInnerView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropZonePlaceholder: {
    width: 36,
    height: 36,
    margin: 1,
  },
  gameAreaContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  checkButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  solveKeywordButton: {
    backgroundColor: '#1BDB55',
  },
  keyboardViewContainer: {
    width: '100%',
    height: 200,
    marginTop: 50,
    position: 'absolute',
  },
  checkAnswerButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  spacer: {
    width: 16,
  },
  checkAnswerButton: {
    backgroundColor: '#D32F2F',
  },
  placeholderText: {
    color: '#999',
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
  },
  wordContainer: {
    margin: 6,
    backgroundColor: '#FCF8E8',
    borderRadius: 10,
    shadowColor: '#32325D',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  wordText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
  },
  wordsContainer: {
    marginTop: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 70,
  },
  checkButton: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignSelf: 'center',
    marginBottom: 30,
  },
  checkButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    marginHorizontal: 16,
  },
  answerContainer: {
    position: 'relative',
    alignItems: 'flex-start',
    marginTop: 32,
    backgroundColor: '#FCF8E8',
    borderRadius: 15,
    paddingVertical: 32,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  answerContainerError: {
    borderColor: '#FF6B6B',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerContainerCorrect: {
    borderColor: '#2EB553',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  errorText: {
    fontSize: 12,
    textAlign: 'center',
    backgroundColor: '#FDE83280',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginTop: 5,
  },
  correctText: {
    fontSize: 12,
    textAlign: 'center',
    backgroundColor: '#FDE83280',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#2EB553',
    fontWeight: 'bold',
    marginTop: 5,
  },
  modalContainer: {
    zIndex: 1000,
  },
  hiddenInput: {
    width: 0, // Chiều rộng 0
    height: 0, // Chiều cao 0
    opacity: 0, // Hoàn toàn trong suốt
    position: 'absolute', // Không chiếm không gian trong layout
    // Để đảm bảo nó thực sự không nhìn thấy và không tương tác ngoài ý muốn:
    top: -10000, // Đẩy ra rất xa màn hình
    left: -10000,
  },
  // Loading UI styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#112164',
    marginTop: 16,
    textAlign: 'center',
  },
  loadingSubText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  // Error UI styles
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#D32F2F',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  retryButton: {
    backgroundColor: '#1BDB55',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  // Audio styles
  questionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FCF8E8',
    padding: 12,
    borderRadius: 12,
  },
  audioButton: {
    marginRight: 10,
    padding: 6,
    borderRadius: 18,
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
  },
  audioIcon: {
    fontSize: 18,
  },
  questionTitleContainer: {
    flex: 1,
  },
  questionTitle: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
