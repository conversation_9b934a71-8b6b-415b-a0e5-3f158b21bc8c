import {createSlice, createAsyncThunk} from '@reduxjs/toolkit';
import {
  SakuTBGameState,
  GetGameConfigRequest,
  GameConfig,
} from '../../../modules/game/sakutimban/types/sakuTBTypes';
import {shuffleArray} from '../../../utils/arrayUtils';
import sakuTBDA from '../../../modules/game/sakutimban/da/sakuTBDA';

// New state interface using SakuTBGameState
const initialState: SakuTBGameState = {
  // API Data
  questions: [],
  gameConfig: null,

  // Current Question
  listQuestions: [],
  questionLv1: [],
  questionLv2: [],
  questionLv3: [],
  currentQuestion: null,

  // Game Items (current question)
  listItemsLeft: [],
  listItemsRight: [],

  // Game State
  selectedLeft: null,
  selectedRight: null,
  matchedPairs: [],
  errorPairs: [],
  listItemsDone: [],

  // Progress
  questionDone: 0,
  totalQuestion: 0,
  currentStage: 1,
  currentLevel: 1,
  maxLevel: 3,

  // Scoring & Config (from GameConfig API)
  maxLives: 3,
  currentLives: 3,
  timeLimit: 300,
  timeRemaining: 300,
  scorePerLife: 10,
  bonusScore: 50,
  currentScore: 0,

  // API State
  error: null,
  initialized: false,
  competenceId: '',
};

export const SakuTBReducer = createSlice({
  name: 'SakuTBReducer',
  initialState,
  reducers: {
    setData(
      state,
      action: {payload: {stateName: keyof SakuTBGameState; value: any}},
    ) {
      (state as any)[action.payload.stateName] = action.payload.value;
    },
    applyGameConfig: (state, action: {payload: GameConfig}) => {
      const config = action.payload;
      state.gameConfig = config;
      state.maxLives = config.maxLives;
      state.currentLives = config.maxLives;
      state.timeLimit = config.timeLimit;
      state.timeRemaining = config.timeLimit;
      state.scorePerLife = config.scorePerLife;
      state.bonusScore = config.bonusScore;

      console.log('[Redux] Game config applied:', {
        maxLives: config.maxLives,
        timeLimit: config.timeLimit,
        scorePerLife: config.scorePerLife,
        bonusScore: config.bonusScore,
      });
    },
    calculateFinalScore: state => {
      if (state.gameConfig) {
        const finalScore = sakuTBDA.calculateScore(
          state.gameConfig,
          state.currentLives,
          state.maxLives,
        );
        state.currentScore = finalScore;

        console.log('[Redux] Final score calculated:', finalScore);
      }
    },
    startGame: state => {
      state.currentLevel = 1;
      let listQuestions = state.questions.filter(
        question => question.level === state.currentLevel,
      );
      listQuestions = shuffleArray(listQuestions);
      listQuestions = state.listQuestions = listQuestions.map(
        (question, index) => ({
          ...question,
          sort: index,
        }),
      );
      state.questionDone = 0;
      state.totalQuestion = state.listQuestions.length;
      state.currentQuestion = state.listQuestions[state.questionDone];
      state.listItemsLeft = shuffleArray([...state.currentQuestion.leftItems]);
      state.listItemsRight = shuffleArray([
        ...state.currentQuestion.rightItems,
      ]);
      state.selectedLeft = null;
      state.selectedRight = null;
      state.matchedPairs = [];
      state.errorPairs = [];
      state.listItemsDone = [];
    },
    nextLevel: state => {
      state.currentLevel += 1;
      let listQuestions = state.questions.filter(
        question => question.level === state.currentLevel,
      );
      listQuestions = shuffleArray(listQuestions);
      listQuestions = state.listQuestions = listQuestions.map(
        (question, index) => ({
          ...question,
          sort: index,
        }),
      );
      state.questionDone = 0;
      state.totalQuestion = state.listQuestions.length;
      state.currentQuestion = state.listQuestions[state.questionDone];
      state.listItemsLeft = shuffleArray([...state.currentQuestion.leftItems]);
      state.listItemsRight = shuffleArray([
        ...state.currentQuestion.rightItems,
      ]);
      state.selectedLeft = null;
      state.selectedRight = null;
      state.matchedPairs = [];
      state.errorPairs = [];
      state.listItemsDone = [];
    },
    nextQuestion: state => {
      state.questionDone += 1;
      state.currentQuestion = state.listQuestions[state.questionDone];
      state.listItemsLeft = shuffleArray([...state.currentQuestion.leftItems]);
      state.listItemsRight = shuffleArray([
        ...state.currentQuestion.rightItems,
      ]);
      state.selectedLeft = null;
      state.selectedRight = null;
      state.matchedPairs = [];
      state.errorPairs = [];
      state.listItemsDone = [];
    },
    resetState: state => {
      state.currentQuestion = null;
      state.listItemsLeft = [];
      state.listItemsRight = [];
      state.selectedLeft = null;
      state.selectedRight = null;
      state.matchedPairs = [];
      state.errorPairs = [];
      state.listItemsDone = [];
      state.questionDone = 0;
      state.totalQuestion = 0;
      state.currentStage = 1;
      state.currentLives = state.maxLives;
      state.timeRemaining = state.timeLimit;
    },
  },
});

export const {
  setData,
  nextQuestion,
  applyGameConfig,
  calculateFinalScore,
  resetState,
  startGame,
  nextLevel,
} = SakuTBReducer.actions;

export default SakuTBReducer.reducer;
