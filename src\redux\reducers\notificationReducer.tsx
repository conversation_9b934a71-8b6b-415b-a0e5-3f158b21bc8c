import {
  Dispatch,
  PayloadAction,
  UnknownAction,
  createSlice,
} from '@reduxjs/toolkit';
import notifee from '@notifee/react-native';
import {DataController} from '../../base/baseController';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import store from '../store/store';

export interface NotificationItem {
  Id: string;
  Name: string;
  DateCreated: number;
  Sort: number;
  Content: string;
  Status: number; // 0: unread, 1: read
  LinkWeb?: string;
  ToiletServicesId?: string;
  CustomerCompanyId?: string;
  LinkApp?: string;
  CustomerId: string;
  ConfigNotificationId: string;
  Type: number;
}

export interface NotificationConfigItem {
  Id: string;
  Name: string;
  DateCreated: number;
  Sort: number;
  Content: string;
  Status: number; // 0: unread, 1: read
  CustomerId: string;
}


interface notificationSimpleResponse {
  data: Array<NotificationItem>;
  badge: number;
  onLoading?: boolean;
  type?: string;
}

const initState: notificationSimpleResponse = {
  data: [],
  badge: 0,
};

export const notificationSlice = createSlice({
  name: 'notification',
  initialState: initState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'GETDATA':
          state.data = action.payload.data;
          state.badge = action.payload.totalCount;
          break;
        case 'GETMORE':
          state.data = [...state.data, ...action.payload.data];
          state.badge = action.payload.totalCount;
          break;
        case 'ADD':
          state.data = [...state.data, ...action.payload.data];
          break;
        case 'SETBADGE':
          state.badge = action.payload.badge;
          break;
        case 'EDIT':
          state.data = state.data.map(e => {
            const _tmp = action.payload.data.find(
              (el: NotificationItem) => el.Id === e.Id,
            );
            return _tmp ?? e;
          });
          break;
        case 'DELETE':
          state.data = state.data.filter(e =>
            action.payload.data.every((id: string) => e.Id !== id),
          );
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    onFetching: state => {
      state.data = [];
      state.onLoading = true;
    },
    onResetNotification: state => {
      state.data = [];
      state.badge = 0;
      state.onLoading = true;
    },
  },
});

const {handleActions, onFetching, onResetNotification} =
  notificationSlice.actions;

export default notificationSlice.reducer;
export {onResetNotification};

export class NotificationActions {
  static setBadge = (dispatch: Dispatch<UnknownAction>) => {
    // dispatch(onFetching())
    notifee.getBadgeCount().then(count =>
      dispatch(
        handleActions({
          type: 'SETBADGE',
          badge: count,
        }),
      ),
    );
  };

  static getData = async (
    dispatch: Dispatch<UnknownAction>,
    config?: {page?: number; status?: number},
  ) => {
    dispatch(onFetching());
    const controller = new DataController('Notification');
    const cusId = store.getState().customer.data?.Id;
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 1000,
      searchRaw: `@CustomerId:{${cusId}}`,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    NotificationActions.setBadge(dispatch);
    dispatch(
      handleActions({
        type: config?.page && config.page > 1 ? 'GETMORE' : 'GETDATA',
        data: res.data,
        totalCount: res.totalCount,
      }),
    );
  };

  static add = async (
    dispatch: Dispatch<UnknownAction>,
    data: Array<NotificationItem>,
  ) => {
    const controller = new DataController('Notification');
    const res = await controller.add(data);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    dispatch(
      handleActions({
        type: 'ADD',
        data: data,
      }),
    );
  };

  static edit = async (
    dispatch: Dispatch<UnknownAction>,
    data: Array<NotificationItem>,
  ) => {
    const controller = new DataController('Notification');
    const res = await controller.edit(data);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    dispatch(
      handleActions({
        type: 'EDIT',
        data: data,
      }),
    );
    return res;
  };

  static delete = async (
    dispatch: Dispatch<UnknownAction>,
    data: Array<string>,
  ) => {
    const controller = new DataController('Notification');
    const res = await controller.delete(data);
    if (res.code !== 200)
      return showSnackbar({
        message: res.message,
        status: ComponentStatus.ERROR,
      });
    dispatch(
      handleActions({
        type: 'DELETE',
        data: data,
      }),
    );
  };

  static reset = (dispatch: Dispatch<UnknownAction>) => {
    dispatch(onResetNotification());
  };
}
