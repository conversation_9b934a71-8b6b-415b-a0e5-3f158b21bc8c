import {DataController} from '../../../base/baseController';
import {getRandomElements} from '../../../utils/arrayUtils';
import {GetQuestionsResponse} from '../sakutimban/types/sakuTBTypes';
import {Question} from './models/models';

export class SakutcDa {
  private questionController: DataController;
  //GameAnswer
  private answerController: DataController;

  constructor() {
    this.questionController = new DataController('GameQuestion');
    this.answerController = new DataController('GameAnswer');
  }

  validateQuestions = (questions: any): Question[] => {
    return questions.map((question: any) => {
      return {
        id: question.Id,
        level: question.Level,
        title: question.Name,
        listWords: question.Answers.map((answer: any) => ({
          id: answer.Id,
          sort: answer.Sort,
          text: answer.Name,
        })),
        sort: question.Sort,
        hint: question.Suggest,
      };
    });
  };

  static loadGameQuestions = async ({
    gameId,
    stage,
    competenceId,
    questionCount,
  }: {
    gameId: string;
    stage: number;
    competenceId: string;
    questionCount: number;
  }) => {
    try {
      const sakuTcDa = new SakutcDa();
      const questions = await sakuTcDa.getQuestionsByGameAndStage(
        gameId,
        stage,
        competenceId,
        questionCount,
      );
      return questions;
    } catch (error) {
      console.error('[Redux] Error loading questions:', error);
      return {
        questions: [],
      };
    }
  };

  async getQuestionsByGameAndStage(
    gameId: string,
    stage: number = 1,
    competenceId: string,
    questionCount: number = 5,
  ): Promise<Question[]> {
    try {
      const response: GetQuestionsResponse =
        await this.questionController.getListSimple({
          query: `@GameId: {${gameId}} @Stage: [${stage}] @Purpose: [${competenceId}]`,
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        });
      if (response.code === 200) {
        const questionLv1 = getRandomElements(
          response.data.filter((question: any) => question.Level === 1),
          questionCount,
        );
        const questionLv2 = getRandomElements(
          response.data.filter((question: any) => question.Level === 2),
          questionCount,
        );
        const questionLv3 = getRandomElements(
          response.data.filter((question: any) => question.Level === 3),
          questionCount,
        );
        let questions = [...questionLv1, ...questionLv2, ...questionLv3];

        const answerResponse = await this.answerController.getListSimple({
          query: `@GameQuestionId: {${questions
            .map((q: any) => q.Id)
            .join(' | ')}}`,
        });
        if (
          answerResponse &&
          answerResponse.data &&
          answerResponse.data.length > 0
        ) {
          questions.forEach((question: any, index: number) => {
            question.Answers = answerResponse.data.filter(
              (answer: any) => answer.GameQuestionId === question.Id,
            );
            question.Sort = index + 1;
          });
        }

        const dataValidate = this.validateQuestions(questions);
        console.log('🚀 ~ SakutcDa ~ dataValidate:', dataValidate);
        return dataValidate;
      } else {
        throw new Error(
          `API Error: ${response.message} (Code: ${response.code})`,
        );
      }
    } catch (error) {
      console.error('[MghhDa] Error fetching questions:', error);
      throw Error('Failed to fetch questions from API');
    }
  }

  static async getGameConfig(gameId: string): Promise<any> {
    try {
      const controller = new DataController('GameConfig');
      const response = await controller.getListSimple({
        query: `@GameId: {${gameId}}`,
      });
      if (
        response.code !== 200 ||
        !response.data ||
        response.data.length === 0
      ) {
        throw new Error(
          'No game config found or API returned unsuccessful response',
        );
      }
      const configData = response.data.find((item: any) => item.Sort === 1);
      const configLv1 = response.data.find((item: any) => item.Sort === 2);
      const configLv2 = response.data.find((item: any) => item.Sort === 3);
      const configLv3 = response.data.find((item: any) => item.Sort === 4);

      return {
        configLv1: configLv1,
        configLv2: configLv2,
        configLv3: configLv3,
        config: configData,
      };
    } catch (error) {
      console.error('[GameConfigDA] Error loading game config:', error);
      throw error;
    }
  }
}
