import {ScrollView, Text, View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../../Screen/Layout/header';
import {navigateBack, RootScreen} from '../../../router/router';
import {AppButton, HashTag, ListTile, Winicon} from 'wini-mobile-components';
import WScreenFooter from '../../../Screen/Layout/footer';
import {TypoSkin} from '../../../assets/skin/typography';
import {useNavigation} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ExamType, StatusExam} from '../../../Config/Contanst';
import {useTranslation} from 'react-i18next';
import {CourseDA} from '../../Course/da';
import {DataController} from '../../../base/baseController';

export default function OverviewTest(pros: any) {
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const [lessonData, setLessonData] = useState<any>([]);
  const [listQuestion, setListQuestion] = useState<any>([]);
  const [lstExam, setLstExam] = useState<any>([]);
  useEffect(() => {
    fetchData();
  }, []);
  //lấy detail của lesson
  const fetchData = async () => {
    const da = new CourseDA();
    const result = await da.getLessonDetail(pros.lessonData.Id);
    if (result) {
      const examIds = result.data?.ExamId?.split(',');
      if (examIds && examIds.length > 0) {
        const examcontroller = new DataController('Exam');
        await examcontroller
          .getPatternList({
            page: 1,
            size: examIds.length,
            query: `@Id:{${examIds.join(' | ')}} @Type:[${ExamType.quiz}]`,
            pattern: {
              QuestionId: [
                'Id',
                'Name',
                'Title',
                'SelectionType',
                'Score',
                'Audio',
                'Img',
              ],
            },
          })
          .then(res => {
            if (res.Question) {
              setLstExam(res.data.sort((a: any, b: any) => a.Sort - b.Sort));

              // check selectionType == null => 1 chon 1
              if (res.Question) {
                res.Question = res.Question?.map((item: any) => {
                  return {...item, SelectionType: item?.SelectionType || 1};
                });
              }
              setListQuestion(
                res.Question.sort((a: any, b: any) => {
                  const aExam = res.data.find((e: any) =>
                    e.QuestionId.includes(a.Id),
                  );
                  const bExam = res.data.find((e: any) =>
                    e.QuestionId.includes(b.Id),
                  );
                  if (aExam.Id === bExam.Id)
                    return (
                      aExam.QuestionId.split(',').findIndex(
                        (e: any) => e === a.Id,
                      ) -
                      bExam.QuestionId.split(',').findIndex(
                        (e: any) => e === b.Id,
                      )
                    );
                  else return aExam.Sort - bExam.Sort;
                }),
              );
            }
          });
      }
      setLessonData(result.data);
    }
  };
  function CheckResult({item}: any) {
    const isPass = item?.Status == StatusExam.passed;
    return (
      <Text
        style={{
          ...TypoSkin.title3,
          color: isPass
            ? ColorThemes.light.Success_Color_Main
            : ColorThemes.light.Error_Color_Main,
        }}>
        {isPass ? t('exam.pass') : t('exam.fail')}
      </Text>
    );
  }

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      {/* content */}
      <ScrollView style={{flex: 1}}>
        <View style={{gap: 24}}>
          <ListTile
            style={{paddingHorizontal: 16, padding: 0}}
            leading={<Winicon src="fill/user interface/c-question" size={20} />}
            title={t('exam.totalQuestions', {count: listQuestion?.length ?? 0})}
            bottom={
              lstExam?.length > 0 ? (
                <View
                  style={{
                    flex: 1,
                    width: '100%',
                    paddingTop: 16,
                    flexWrap: 'wrap',
                    flexDirection: 'row',
                  }}>
                  {lstExam.map((item: any, index: number) => {
                    return (
                      <HashTag
                        key={index}
                        styles={{
                          marginRight: 8,
                          marginBottom: 8,
                          backgroundColor:
                            ColorThemes.light.Secondary_3_Color_Background,
                          borderRadius: 8,
                          padding: 8,
                          paddingHorizontal: 16,
                          borderWidth: 1,
                          borderColor:
                            ColorThemes.light.Secondary_3_Color_Border,
                        }}
                        textStyles={{
                          ...TypoSkin.buttonText6,
                          color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                        }}
                        // title={`${item.Name} (${item.Time ?? 0} phút)`}
                        title={`${item.Name}`}
                      />
                    );
                  })}
                </View>
              ) : null
            }
          />
          <ListTile
            style={{paddingHorizontal: 16, padding: 0}}
            leading={<Winicon src="fill/user interface/clock" size={20} />}
            title={t('exam.timeLimit', {minutes: lessonData?.Time ?? 0})}
          />
          <ListTile
            style={{paddingHorizontal: 16, padding: 0}}
            leading={<Winicon src="fill/user interface/c-warning" size={20} />}
            title={t('exam.requirements')}
            bottom={
              <View style={{flex: 1, width: '100%', paddingTop: 16}}>
                <Text style={{...TypoSkin.body3}}>
                  {t('exam.passingRequirement', {
                    percent: lessonData?.QuizzScore ?? 0,
                  })}
                </Text>
              </View>
            }
          />
          {/* <ListTile
            style={{paddingHorizontal: 16, padding: 0}}
            leading={
              <Winicon src="fill/user interface/warning-sign" size={20} />
            }

            title={
              
                `${t('exam.results', {
                    count: examInfor?.TestResults?.length ?? 0,
                  })}`
                : `Kết quả ${examInfor?.CountTest ?? 0}/${
                    examInfor?.Count ?? 0
                  }`
            }
            // bottom={
            //   loading ? (
            //     <View
            //       style={{
            //         flex: 1,
            //         width: '100%',
            //         paddingTop: 16,
            //         paddingBottom: 100,
            //       }}>
            //       <Text style={{...TypoSkin.body3}}>...</Text>
            //     </View>
            //   ) : (
            //     <View
            //       style={{
            //         flex: 1,
            //         width: '100%',
            //         paddingTop: 16,
            //         paddingBottom: 100,
            //       }}>
            //       {examInfor?.TestResults?.length ? (
            //         examInfor?.TestResults?.map((item: any, index: number) => {
            //           return (
            //             <ListTile
            //               key={index}
            //               onPress={
            //                 examInfor.Type === ExamType.Real
            //                   ? undefined
            //                   : () => {
            //                       navigate(RootScreen.tryingHistoryTest, {
            //                         id: item.Id,
            //                         examId: item.ExamId,
            //                       });
            //                     }
            //               }
            //               style={{padding: 0, paddingVertical: 8}}
            //               title={
            //                 <View
            //                   style={{
            //                     width: '100%',
            //                     gap: 8,
            //                     flexDirection: 'row',
            //                     alignItems: 'center',
            //                   }}>
            //                   <Text
            //                     style={{
            //                       ...TypoSkin.heading7,
            //                       color:
            //                         ColorThemes.light.Neutral_Text_Color_Title,
            //                     }}>
            //                     {t('exam.attempt', {number: index + 1})}:
            //                   </Text>
            //                   <CheckResult item={item} />
            //                 </View>
            //               }
            //               subtitle={
            //                 <View
            //                   style={{
            //                     width: '100%',
            //                     paddingTop: 4,
            //                     alignContent: 'flex-start',
            //                   }}>
            //                   <Text
            //                     style={{
            //                       ...TypoSkin.subtitle3,
            //                       color:
            //                         ColorThemes.light
            //                           .Neutral_Text_Color_Subtitle,
            //                     }}>
            //                     {`${Ultis.datetoString(
            //                       new Date(item.DateCreated ?? 0),
            //                       'dd/MM/yyyy hh:mm',
            //                     )}`}
            //                   </Text>
            //                 </View>
            //               }
            //               trailing={
            //                 examInfor.Type === ExamType.Real ? null : (
            //                   <View
            //                     style={{
            //                       width: '100%',
            //                     }}>
            //                     <Winicon
            //                       src="outline/user interface/view"
            //                       color={
            //                         ColorThemes.light
            //                           .Neutral_Text_Color_Subtitle
            //                       }
            //                       size={16}
            //                     />
            //                   </View>
            //                 )
            //               }
            //             />
            //           );
            //         })
            //       ) : (
            //         <Text style={{...TypoSkin.body3}}>
            //           {t('exam.noResults')}
            //         </Text>
            //       )}
            //     </View>
            //   )
            // }
          /> */}
        </View>
      </ScrollView>
      {/*  */}
      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          paddingBottom: 32,
        }}>
        <AppButton
          title={t('exam.startExam')}
          backgroundColor={ColorThemes.light.Primary_Color_Main}
          borderColor="transparent"
          // disabled={(examInfor?.CountTest ?? 0) >= (examInfor?.Count ?? 0)}
          containerStyle={{
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            navigation.push(RootScreen.doingTestNew, {
              lessonData: lessonData,
              lstExam: lstExam,
              listQuestions: listQuestion,
              Step: pros.step,
            });
          }}
          textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
}
