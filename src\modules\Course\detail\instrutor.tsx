/* eslint-disable react-native/no-inline-styles */
import {Image, ScrollView, Text, View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {ListTile} from 'wini-mobile-components';
import {TypoSkin} from '../../../assets/skin/typography';
import {useEffect, useState} from 'react';
import {CustomerDA} from '../../customer/da';
import ConfigAPI from '../../../Config/ConfigAPI';
import {RootScreen} from '../../../router/router';
import {useNavigation} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';

export default function Instructor(pros: any) {
  const [data, setData] = useState<any>();
  const customerDA = new CustomerDA();
  const navigation = useNavigation<any>();
  useEffect(() => {
    getData();
  }, []);
  const getData = async () => {
    const customer = await customerDA.getCustomerbyId(pros.data.CustomerId);
    const customerf = await customerDA.getFollowCustomer(pros.data.CustomerId);
    if (customer) {
      const tmp = {
        ...customer.data,
        title: customerf
          ? `${customerf.follower?.length ?? 0} Người theo dõi * ${
              customerf.following?.length ?? 0
            } Đang theo dõi`
          : '',
      };
      setData(tmp);
    }
  };
  return (
    <ScrollView
      ref={pros.scrollviewRef}
      {...pros.refreshControlProps}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <ListTile
        onPress={() => {
          navigation.push(RootScreen.ProfileCommunity, {
            Id: pros.data.CustomerId,
            forEschool: true,
          });
        }}
        leading={
          <View
            style={{
              width: 56,
              height: 56,
              borderRadius: 100,
              overflow: 'hidden',
            }}>
            {data?.AvatarUrl ? (
              <FastImage
                source={
                  data?.AvatarUrl
                    ? {
                        uri: data?.AvatarUrl?.includes('http')
                          ? data?.AvatarUrl
                          : ConfigAPI.getValidLink(data?.AvatarUrl),
                      }
                    : require('../../../assets/appstore.png')
                }
                style={{
                  width: '100%',
                  height: '100%',
                  borderRadius: 100,
                }}
                resizeMode="cover"
              />
            ) : (
              // show first text
              <View
                style={{
                  width: '100%',
                  height: '100%',
                  borderRadius: 100,
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: ColorThemes.light.Primary_Color_Main,
                }}>
                <Text
                  style={{
                    ...TypoSkin.heading6,
                    color: ColorThemes.light.Neutral_Background_Color_Absolute,
                  }}>
                  {data?.Name ? data?.Name.charAt(0).toUpperCase() : ''}
                </Text>
              </View>
            )}
          </View>
        }
        listtileStyle={{gap: 16}}
        title={`${data?.Name ?? ''}`}
        subtitle={`${data?.title ?? ''}`}
        bottom={
          <View style={{flex: 1, width: '100%', paddingTop: 16}}>
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              }}>
              {`${data?.Description ?? ''}`}
            </Text>
          </View>
        }
      />
    </ScrollView>
  );
}
