/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {View, Text, FlatList, RefreshControl} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {CourseDA} from '../da';
import {AppButton, FDialog} from 'wini-mobile-components';
import {
  DefaultProduct,
  SkeletonPlaceCard,
} from '../../Default/card/defaultProduct';
import {useTranslation} from 'react-i18next';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../router/router';
import {CustomerDA} from '../../customer/da';
import {Ultis} from '../../../utils/Utils';
import {StorageContanst} from '../../../Config/Contanst';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import EmptyPage from '../../../Screen/emptyPage';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
  id: string;
  onPressSeeMore?: () => void;
  onRefresh?: () => void;
}

export default function JapanBasic(props: Props) {
  const [data, setData] = useState<Array<any>>([]);
  const [isRefresh, setRefresh] = useState(false);
  const [isLoading, setLoading] = useState(true);
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);
  const courseDA = new CourseDA();
  const customerDA = new CustomerDA();
  useEffect(() => {
    getData();
  }, []);
  const getData = async () => {
    const result = await courseDA.getAllListbyCategory(1, 10, props.id);

    if (result) {
      const listCustomerId = result.data.map((item: any) => item.CustomerId);
      const listCustomer = await customerDA.getCustomersByIds(listCustomerId);
      const lst2 = await Promise.all(
        result.data.map(async (item: any) => {
          const countStudent = await courseDA.countStudentCourse(item.Id);
          const customer = listCustomer?.data.find(
            (a: any) => a.Id === item.CustomerId,
          );

          if (customer) {
            return {
              ...item,
              IsLike: await courseDA.checkCourseIsWishlishCustomer(item.Id),
              listItems: {
                id: item.Id,
                icon: 'outline/education/hat-3',
                title: `${countStudent} ${t('student')}`,
              },
              relativeUser: {
                image: customer.AvatarUrl,
                title: `${customer.Name}`,
              },
            };
          } else {
            return {
              ...item,
              listItems: {
                id: item.Id,
                icon: 'outline/education/hat-3',
                title: `${countStudent} ${t('student')}`,
              },
            };
          }
        }),
      );
      setData(lst2);
    }
    setLoading(false);
    setRefresh(false);
  };

  return (
    <View
      style={{
        height: data.length == 0 && !isLoading
          ? 50
          :  props.horizontal ? 455 : undefined,
      }}>

      <FDialog ref={dialogRef} />

      {props.titleList ? (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            flexDirection: 'row',
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}>
          <Text
            style={{
              ...TypoSkin.heading5,
              color: ColorThemes.light.Neutral_Text_Color_Title,
            }}>
            {props.titleList}
          </Text>
          {props.isSeeMore ? (
            <AppButton
              title={'Xem thêm'}
              containerStyle={{
                justifyContent: 'flex-start',
                alignSelf: 'center',
              }}
              backgroundColor={'transparent'}
              textStyle={TypoSkin.buttonText3}
              borderColor="transparent"
              suffixIconSize={16}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              onPress={props.onPressSeeMore}
              textColor={ColorThemes.light.Info_Color_Main}
            />
          ) : null}
        </View>
      ) : null}
      {data.length > 0 ? (
        <FlatList
          data={data}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          // refreshControl={
          //   <RefreshControl
          //     refreshing={isRefresh}
          //     onRefresh={() => {
          //       onRefresh();
          //     }}
          //   />
          // }
          contentContainerStyle={{
            paddingRight: 32,
            gap: 16,
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}
          renderItem={({item, index}) => {
            return (
              <DefaultProduct
                key={index}
                listItems={item.listItems}
                onPressDetail={() => {
                  navigation.push(RootScreen.CourseDetail, {id: item.Id});
                }}
                flexDirection="default"
                containerStyle={{padding: 16}}
                onPressLikeAction={async () => {
                  var accessToken = await getDataToAsyncStorage(
                    StorageContanst.accessToken,
                  );
                  if (accessToken) {
                    if (item.IsLike === true) {
                      const result = await courseDA.deleteWishlistCourse(
                        item.Id,
                      );
                      if (result.code === 200) {
                        setData(prevData =>
                          prevData.map(a =>
                            a.Id === item.Id ? {...item, IsLike: false} : a,
                          ),
                        );
                      }
                    } else {
                      const result = await courseDA.addWishlistCourse(item.Id);
                      if (result) {
                        setData(prevData =>
                          prevData.map(a =>
                            a.Id === item.Id ? {...item, IsLike: true} : a,
                          ),
                        );
                      }
                    }
                  } else {
                    //TODO: add wishlish chưa có token thì navigate tới login và truyền theo router.
                    //CHIENV: DONE
                    // dialogCheckAcc(dialogRef);
                  }
                }}
                data={item}
                titleStyle={{
                  ...TypoSkin.heading7,
                  color: ColorThemes.light.Neutral_Text_Color_Body,
                }}
              />
            );
          }}
          style={{width: '100%', height: '100%'}}
          keyExtractor={item => item.Id?.toString()}
          horizontal={true}
          ListEmptyComponent={() => {
            if (isLoading) {
              return <SkeletonPlaceCard />;
            }
            return <EmptyPage title={t('nodata')} />;
          }}
        />
      ) : (
        <></>
      )}
    </View>
  );
}
