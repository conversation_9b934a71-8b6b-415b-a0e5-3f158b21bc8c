/* eslint-disable react-native/no-inline-styles */
import {
  Animated,
  ActivityIndicator,
  Dimensions,
  FlatList,
  Pressable,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useRef, useState, useEffect} from 'react';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import NewFeed from './newfeed';
import About from './about';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  AppButton,
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  SkeletonImage,
  TextField,
  Winicon,
} from 'wini-mobile-components';
import {navigate, navigateBack, RootScreen} from '../../../router/router';
import {onShare} from '../../../features/share';
import {DataController} from '../../../base/baseController';
import {useRoute} from '@react-navigation/native';
import {GroupDA} from '../groups/da';
import ConfigAPI from '../../../Config/ConfigAPI';
import {useDispatch} from 'react-redux';
import store, {AppDispatch} from '../../../redux/store/store';
import {followingGroupsActions} from '../reducers/followingGroupsReducer';
import ImagePicker from 'react-native-image-crop-picker';
import {BaseDA} from '../../../base/BaseDA';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {SkeletonPlaceCard} from '../../Default/card/defaultPost';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import MembersGroup from './listview/members';
import {SkeletonFriendCard} from '../pages/friends';
import {CustomerDA} from '../../customer/da';
import EmptyPage from '../../../Screen/emptyPage';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import {confirmGroupsActions} from '../reducers/confirmGroupsReducer';
import FastImage from 'react-native-fast-image';

export default function GroupIndex() {
  const [groupDetail, setGroupDetail] = useState<any>();
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [isFollowing, setIsFollowing] = useState(false);
  const [isHeaderVisible, setIsHeaderVisible] = useState(false);
  const customer = useSelectorCustomerState().data;
  const route = useRoute<any>();
  const {Id} = route.params;
  const scrollY = useRef(new Animated.Value(0)).current;
  const groupDA = new GroupDA();
  const dispatch: AppDispatch = useDispatch();
  const currentUser = useSelectorCustomerState().data;
  const bottomSheetRef = useRef<any>(null);
  const [role, setRole] = useState(0);
  const dialogRef = useRef<any>(null);
  // Function to fetch group details
  const fetchGroupDetail = async () => {
    setIsLoading(true);
    try {
      const groupController = new DataController('Group');
      const result = await groupController.getById(Id);
      if (result.code === 200) {
        const memberResult = await groupDA.getGroupMembers(result.data.Id);
        const memberCount = memberResult ? memberResult.length : 0;
        setGroupDetail({
          ...result?.data,
          memberCount: memberCount,
          memberList: memberResult ?? [],
        });
      }
    } catch (error) {
      console.error('Failed to fetch group detail:', error);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  // Handle pull-to-refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchGroupDetail();
    await checkRole();
    await checkFollowStatus();
  };

  // Check user role in the group
  const checkRole = async () => {
    if (!Id || !customer) return;
    const role = await groupDA.getRoles(Id, customer?.Id);
    setRole(role);
  };

  // Check if user is following the group
  const checkFollowStatus = async () => {
    if (!Id) return;
    const followStatus = await groupDA.isFollowingGroup(Id);
    setIsFollowing(followStatus);
  };

  useEffect(() => {
    if (!Id) return;
    fetchGroupDetail();
  }, [Id]);
  useEffect(() => {
    if (!Id) return;
    checkRole();
  }, [Id]);

  useEffect(() => {
    if (!Id) return;
    checkFollowStatus();
  }, [Id]);

  const headerHeight = 400; // Chiều cao phần header ban đầu
  const tabBarHeight = 50; // Chiều cao của tab bar

  // Animation cho header mới và tabbar
  const newHeaderOpacity = scrollY.interpolate({
    inputRange: [headerHeight - 50, headerHeight - 10],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  // Animation cho header gốc - ẩn dần khi cuộn xuống
  const originalHeaderOpacity = scrollY.interpolate({
    inputRange: [headerHeight - 50, headerHeight - 10],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  // Chuyển đổi giữa các tab
  const changeTab = (tab: any) => {
    setActiveTab(tab);
  };

  const tabs = [
    {Id: 0, Name: 'Thảo luận'},
    {Id: 1, Name: 'Thành viên'},
    {Id: 2, Name: 'Thông tin'},
  ];

  const [img, setImg] = useState<any>(null);
  const pickerImg = async () => {
    const img = await ImagePicker.openPicker({
      multiple: false,
      cropping: false,
    });
    if (img) {
      const resImgs = await BaseDA.uploadFiles([
        {
          uri: img.path,
          type: img.mime,
          name: img.filename ?? 'new file img',
        },
      ]);
      if (resImgs) {
        const rs = await groupDA.edit({
          Id: groupDetail?.Id,
          Name: groupDetail?.Name,
          Description: groupDetail?.Description,
          CustomerId: groupDetail?.CustomerId,
          DateCreated: groupDetail?.DateCreated,
          Privacy: groupDetail?.Privacy,
          Status: groupDetail?.Status,
          Type: groupDetail?.Type,
          Avatar: groupDetail?.Avatar,
          Thumb: resImgs[0].Id,
        });
        if (rs.code == 200) {
          setImg(resImgs[0].Id);
          setGroupDetail({
            ...groupDetail,
            Thumb: resImgs[0].Id,
          });
        }
      }
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      {isLoading ? (
        <SkeletonGroupIndex />
      ) : (
        <>
          <FBottomSheet ref={bottomSheetRef} />
          <FDialog ref={dialogRef} />
          {/* Header mới - xuất hiện khi cuộn */}
          <Animated.View
            style={[
              styles.newHeader,
              {
                opacity: newHeaderOpacity,
                // shadowOpacity: fixedElementsShadow,
              },
            ]}
            // Chỉ cho phép tương tác khi header đã hiển thị (opacity > 0)
            pointerEvents={isHeaderVisible ? 'auto' : 'none'}>
            <SafeAreaView style={styles.safeAreaTop} edges={['top']} />
            <ListTile
              isClickLeading
              style={styles.headerListTile}
              leading={
                <TouchableOpacity
                  style={styles.backButton}
                  activeOpacity={0.7} // Thêm hiệu ứng khi nhấn
                  onPress={() => {
                    navigateBack();
                  }}>
                  <Winicon
                    src="outline/arrows/left-arrow"
                    size={24} // Tăng kích thước icon
                    color={ColorThemes.light.Neutral_Text_Color_Title}
                  />
                </TouchableOpacity>
              }
              title={groupDetail?.Name}
              titleStyle={styles.headerTitle}
              subtitle={`${groupDetail?.memberCount} thành viên`}
              subTitleStyle={styles.headerSubtitle}
              trailing={
                <TouchableOpacity
                  style={styles.shareButton}
                  onPress={() => {
                    onShare({content: `${ConfigAPI.urlWeb}/groups/d?id=${Id}`});
                  }}>
                  <Winicon
                    src="fill/arrows/social-sharing"
                    size={20}
                    color={ColorThemes.light.Neutral_Text_Color_Title}
                  />
                </TouchableOpacity>
              }
            />
          </Animated.View>

          {/* Content  */}
          <Animated.ScrollView
            bounces={true}
            contentContainerStyle={{paddingTop: headerHeight + tabBarHeight}}
            scrollEventThrottle={16}
            nestedScrollEnabled
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[ColorThemes.light.Primary_Color_Main]}
                tintColor={ColorThemes.light.Primary_Color_Main}
              />
            }
            onScroll={Animated.event(
              [{nativeEvent: {contentOffset: {y: scrollY}}}],
              {
                useNativeDriver: true,
                listener: (event: any) => {
                  // Cập nhật trạng thái hiển thị của header dựa trên vị trí cuộn
                  const offsetY = event.nativeEvent.contentOffset.y;
                  setIsHeaderVisible(offsetY > headerHeight - 30);
                },
              },
            )}>
            {/* Header gốc - biến mất khi cuộn */}
            <Animated.View
              style={[
                styles.originalHeader,
                {
                  opacity: originalHeaderOpacity, // Áp dụng opacity animation
                  // Tắt tương tác khi header gốc ẩn đi
                  pointerEvents: 'auto',
                  zIndex: 1, // Đảm bảo header gốc nằm dưới header mới
                },
              ]}>
              <Pressable>
                {/* image */}
                <View style={styles.imageContainer}>
                  <AppButton
                    prefixIcon={'outline/arrows/left-arrow'}
                    prefixIconSize={20}
                    backgroundColor={
                      ColorThemes.light.Neutral_Background_Color_Absolute
                    }
                    textColor={ColorThemes.light.Neutral_Text_Color_Title}
                    borderColor="transparent"
                    containerStyle={styles.backButtonOriginal}
                    onPress={navigateBack}
                  />

                  <AppButton
                    prefixIcon={'fill/arrows/social-sharing'}
                    prefixIconSize={20}
                    backgroundColor={
                      ColorThemes.light.Neutral_Background_Color_Absolute
                    }
                    textColor={ColorThemes.light.Neutral_Text_Color_Title}
                    borderColor="transparent"
                    containerStyle={styles.shareButtonOriginal}
                    onPress={() => {
                      onShare({content: 'group'});
                    }}
                  />
                  {groupDetail?.CustomerId === currentUser?.Id ? (
                    <AppButton
                      prefixIcon={'outline/entertainment/camera'}
                      prefixIconSize={16}
                      title={'Edit'}
                      backgroundColor={
                        ColorThemes.light.Neutral_Background_Color_Absolute
                      }
                      textColor={ColorThemes.light.Neutral_Text_Color_Title}
                      borderColor="transparent"
                      containerStyle={styles.editButton}
                      onPress={pickerImg}
                    />
                  ) : null}
                  <FastImage
                    key={groupDetail?.Thumb}
                    source={
                      groupDetail?.Thumb
                        ? {
                            uri: ConfigAPI.getValidLink(groupDetail?.Thumb),
                          }
                        : require('../../../assets/appstore.png')
                    }
                    style={styles.coverImage}
                  />
                </View>
                <ListTile
                  title={groupDetail?.Name}
                  titleStyle={styles.groupTitle}
                  subtitle={`${groupDetail?.memberCount} thành viên`}
                  subTitleStyle={styles.groupSubtitle}
                  bottom={
                    <View style={styles.groupBottomContainer}>
                      <TouchableOpacity
                        onPress={() => {
                          if (!customer) {
                            dialogCheckAcc(dialogRef);
                            return;
                          }
                          showBottomSheet({
                            ref: bottomSheetRef,
                            // enableDismiss: true,
                            title: 'Thành viên',
                            suffixAction: <View />,
                            prefixAction: (
                              <TouchableOpacity
                                onPress={() => hideBottomSheet(bottomSheetRef)}
                                style={styles.closeButton}>
                                <Winicon
                                  src="outline/layout/xmark"
                                  size={20}
                                  color={
                                    ColorThemes.light.Neutral_Text_Color_Body
                                  }
                                />
                              </TouchableOpacity>
                            ),
                            children: (
                              <MembersGroup
                                Members={groupDetail.memberList}
                                GroupId={Id}
                                role={role}
                                scrollEnabled={true}
                              />
                            ),
                          });
                        }}
                        style={styles.membersContainer}>
                        {groupDetail?.memberList?.length > 0 &&
                          groupDetail?.memberList
                            ?.slice(0, 4)
                            .map((item: any, index: number) => {
                              return (
                                <View
                                  key={`user-${item?.Id || index}`}
                                  style={styles.memberItemContainer}>
                                  <View
                                    style={[
                                      styles.memberAvatar,
                                      {left: index * 20},
                                    ]}>
                                    <FastImage
                                      source={
                                        item?.AvatarUrl
                                          ? {
                                              uri: item?.AvatarUrl?.includes(
                                                'https',
                                              )
                                                ? item?.AvatarUrl
                                                : ConfigAPI.getValidLink(
                                                    item?.AvatarUrl,
                                                  ),
                                            }
                                          : require('../../../assets/appstore.png')
                                      }
                                      style={styles.avatarImage}
                                    />
                                  </View>
                                  {index === 3 && (
                                    <View
                                      style={[
                                        styles.moreButton,
                                        {left: 3 * 20},
                                      ]}>
                                      <Winicon
                                        src="fill/user interface/menu-dots"
                                        color={
                                          ColorThemes.light
                                            .Neutral_Text_Color_Subtitle
                                        }
                                        size={12}
                                      />
                                    </View>
                                  )}
                                </View>
                              );
                            })}
                      </TouchableOpacity>
                      {/* buttons */}
                      <View style={styles.buttonsContainer}>
                        {groupDetail?.CustomerId === currentUser?.Id ? (
                          <></>
                        ) : (
                          <AppButton
                            title={isFollowing ? 'Bỏ theo dõi' : 'Theo dõi'}
                            backgroundColor={
                              isFollowing
                                ? ColorThemes.light
                                    .Neutral_Background_Color_Main
                                : ColorThemes.light.Primary_Color_Main
                            }
                            textColor={
                              isFollowing
                                ? ColorThemes.light.Neutral_Text_Color_Body
                                : ColorThemes.light
                                    .Neutral_Background_Color_Absolute
                            }
                            borderColor="transparent"
                            containerStyle={styles.followButton}
                            onPress={async () => {
                              if (!customer) {
                                dialogCheckAcc(dialogRef);
                                return;
                              }
                              if (isFollowing) {
                                dispatch(
                                  followingGroupsActions.unfollowGroup(Id),
                                );
                              } else {
                                dispatch(
                                  followingGroupsActions.followGroup({
                                    Id,
                                    Name: groupDetail?.Name || '',
                                    ImageUrl: groupDetail?.Thumb || '',
                                    Description: groupDetail?.Description || '',
                                    MemberCount: groupDetail?.memberCount || 0,
                                  }),
                                );
                                const confirmGroup = store
                                  .getState()
                                  .confirmGroups.groups.find(
                                    (item: any) => item.Id === Id,
                                  );
                                if (confirmGroup) {
                                  dispatch(
                                    confirmGroupsActions.removeGroups(Id),
                                  );
                                }
                              }
                              setGroupDetail({
                                ...groupDetail,
                                memberCount: isFollowing
                                  ? groupDetail.memberCount - 1
                                  : groupDetail.memberCount + 1,
                                memberList: isFollowing
                                  ? groupDetail.memberList.filter(
                                      (member: any) =>
                                        member.Id !== customer.Id,
                                    )
                                  : [
                                      ...groupDetail.memberList,
                                      {
                                        Id: customer.Id,
                                        Name: customer.Name,
                                        Img: customer.Img,
                                      },
                                    ],
                              });
                              setIsFollowing(!isFollowing);
                            }}
                          />
                        )}
                        <AppButton
                          prefixIcon={'fill/users/users-add'}
                          prefixIconSize={16}
                          title={'Mời'}
                          backgroundColor={ColorThemes.light.Primary_Color_Main}
                          textColor={
                            ColorThemes.light.Neutral_Background_Color_Absolute
                          }
                          borderColor="transparent"
                          containerStyle={styles.inviteButton}
                          onPress={() => {
                            if (!customer) {
                              dialogCheckAcc(dialogRef);
                              return;
                            }
                            showBottomSheet({
                              ref: bottomSheetRef,
                              enableDismiss: true,
                              title: 'Invite',
                              suffixAction: <View />,
                              prefixAction: (
                                <TouchableOpacity
                                  onPress={() =>
                                    hideBottomSheet(bottomSheetRef)
                                  }
                                  style={styles.closeButton}>
                                  <Winicon
                                    src="outline/layout/xmark"
                                    size={20}
                                    color={
                                      ColorThemes.light.Neutral_Text_Color_Body
                                    }
                                  />
                                </TouchableOpacity>
                              ),
                              children: (
                                <InviteUsers
                                  groupId={Id}
                                  groupMems={groupDetail.memberList}
                                />
                              ),
                            });
                          }}
                        />
                      </View>
                    </View>
                  }
                />
              </Pressable>
            </Animated.View>
            {/* Tabbar - di chuyển lên trên và được ghim */}
            <Animated.View style={[styles.tabBarContainer]}>
              <View style={styles.tabBar}>
                {tabs.map(item => {
                  return (
                    <TouchableOpacity
                      key={item.Id}
                      style={[
                        styles.tabItem,
                        activeTab === item.Id && styles.activeTabItem,
                      ]}
                      onPress={() => changeTab(item.Id)}>
                      <Text
                        style={[
                          styles.tabText,
                          activeTab === item.Id && styles.activeTabText,
                        ]}>
                        {item.Name}
                      </Text>
                      {activeTab === item.Id && (
                        <View style={styles.indicator} />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </Animated.View>
            {activeTab === 0 ? (
              <NewFeed groupId={Id} role={role} />
            ) : activeTab === 1 ? (
              <View style={styles.tabContent}>
                <MembersGroup
                  Members={groupDetail.memberList}
                  role={role}
                  GroupId={Id}
                  scrollEnabled={true}
                  // onUpdateRole={(value: any) => {
                  //   const lstUpdate = groupDetail.memberList.map((member: any) => {
                  //     if(member.Id === value.Id){
                  //       return {...member, Role: value.Role};
                  //     }
                  //     return member;
                  //   });
                  //   setGroupDetail({...groupDetail, memberList: lstUpdate});
                  // }}
                  onDelete={(value: any) => {
                    const lstUpdate = groupDetail.memberList.filter(
                      (member: any) => member.Id !== value.Id,
                    );
                    setGroupDetail({...groupDetail, memberList: lstUpdate});
                  }}
                />
              </View>
            ) : (
              <About
                data={groupDetail}
                isProfile={false}
                onUpdate={(value: any) => {
                  setGroupDetail({
                    ...groupDetail,
                    Name: value.Name,
                    Description: value.Description,
                  });
                }}
              />
            )}
          </Animated.ScrollView>
        </>
      )}
    </SafeAreaView>
  );
}

const InviteUsers = ({
  groupId,
  groupMems,
}: {
  groupId: any;
  groupMems: any[];
}) => {
  const [searchValue, setSearchValue] = useState('');
  const [listFriend, setlistFriend] = useState<Array<any>>([]);
  const customerDA = new CustomerDA();
  // const currentUser = useSelectorCustomerState().data;
  const bottomSheetRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const size = 10;
  const [customerIds, setCustomerIds] = useState(groupMems.map((item: any) => item.Id));

  useEffect(() => {
    getListFriend(1);
  }, [searchValue]);

  const getListFriend = async (pageNumber: number) => {
    if (pageNumber === 1) {
      setIsLoading(true);
    } else {
      setLoadingMore(true);
    }

    const customerController = new DataController('Customer');
    // call api get all customer
    var query = searchValue ? `@Name: (*${searchValue}*)` : '';
    const result = await customerController.getListSimple({
      page: pageNumber,
      size: size,
      query: `-@Id:{${customerIds.join(' | ')}} ${query}`,
    });

    if (result && result.code === 200) {
      if (pageNumber === 1) {
        setlistFriend(result.data);
      } else {
        setlistFriend(prevList => [...prevList, ...result.data]);
      }

      // Check if we have more data to load
      setHasMore(result.data.length === size);
      setPage(pageNumber);
    }

    if (pageNumber === 1) {
      setIsLoading(false);
    } else {
      setLoadingMore(false);
    }
  };
  

  return (
    <View style={styles.inviteContainer}>
      <FBottomSheet ref={bottomSheetRef} />

      <View style={styles.searchContainer}>
        <TextField
          style={styles.searchInput}
          onChange={async (vl: string) => {
            setSearchValue(vl.trim());
          }}
          value={searchValue}
          placeholder="Tìm kiếm..."
          prefix={
            <Winicon
              src="outline/development/zoom"
              size={14}
              color={ColorThemes.light.Neutral_Text_Color_Subtitle}
            />
          }
        />
      </View>
      <View style={styles.inviteListContainer}>
        <FlatList
          data={
            searchValue
              ? listFriend.filter((member: any) =>
                  member.Name.toLowerCase().includes(searchValue.toLowerCase()),
                )
              : listFriend
          }
          style={styles.flatList}
          keyExtractor={(_, index) => index.toString()}
          onEndReached={() => {
            if (hasMore && !loadingMore && !isLoading && !searchValue) {
              getListFriend(page + 1);
            }
          }}
          onEndReachedThreshold={0.5}
          ListEmptyComponent={() => {
            if (isLoading) {
              return (
                <View style={styles.inviteSkeletonContainer}>
                  <SkeletonFriendCard />
                  <SkeletonFriendCard />
                  <SkeletonFriendCard />
                </View>
              );
            }
            return <EmptyPage title="Không có dữ liệu" />;
          }}
          renderItem={({item}) => {
            return (
              <ListTile
                key={item?.Id}
                onPress={() => {
                  navigate(RootScreen.ProfileCommunity, {Id: item?.Id});
                }}
                listtileStyle={styles.inviteListTile}
                leading={
                  <SkeletonImage
                    source={{
                      uri: item?.AvatarUrl
                        ? ConfigAPI.getValidLink(item?.AvatarUrl)
                        : 'https://placehold.co/48/FFFFFF/000000/png',
                    }}
                    style={styles.profileAvatar}
                  />
                }
                title={
                  <Text style={styles.profileName} numberOfLines={1}>
                    {item?.Name}
                  </Text>
                }
                subTitleStyle={styles.profileSubtitle}
                trailing={
                  item?.IsMember == '0' ? (
                    <View style={styles.trailingContainer}>
                      <Text style={styles.sentText}>Đã gửi lời mời</Text>
                    </View>
                  ) : item?.IsMember === '1' ||
                    groupMems?.find(mem => {
                      mem?.Id === item.Id;
                    }) ? (
                    <View style={styles.trailingContainer}>
                      <Text style={styles.joinedText}>Đã tham gia</Text>
                    </View>
                  ) : (
                    <View style={styles.inviteButtonContainer}>
                      <AppButton
                        textColor={
                          ColorThemes.light.Neutral_Background_Color_Absolute
                        }
                        title={'Mời tham gia'}
                        containerStyle={styles.inviteActionButton}
                        backgroundColor={ColorThemes.light.Primary_Color_Main}
                        borderColor="transparent"
                        onPress={async () => {
                          const groupDA = new GroupDA();
                          const result = await groupDA.addMemberInvite(
                            groupId,
                            item?.Id,
                          );
                          if (result) {
                            setlistFriend(
                              listFriend.map((a: any) => {
                                if (a.Id === item.Id) {
                                  return {...a, IsMember: '0'};
                                }
                                return a;
                              }),
                            );
                            setCustomerIds([...customerIds, item.Id]);
                            // hideBottomSheet(bottomSheetRef);
                          }
                        }}
                      />
                    </View>
                  )
                }
              />
            );
          }}
          ListFooterComponent={() => {
            if (loadingMore) {
              return (
                <View style={styles.loadingMore}>
                  <ActivityIndicator
                    color={ColorThemes.light.Primary_Color_Main}
                  />
                </View>
              );
            }
            return <View style={styles.footerSpace} />;
          }}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  originalHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 400,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    zIndex: 1,
  },
  originalHeaderText: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  newHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 3,
  },
  newHeaderText: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  tabBarContainer: {
    position: 'absolute',
    top: 400, // Bắt đầu dưới header gốc
    left: 0,
    right: 0,
    height: 50,
    backgroundColor: '#fff',
    zIndex: 2,
    borderBottomWidth: 0.5,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    height: '100%',
    alignItems: 'center',
  },
  tabItem: {
    paddingVertical: 15,
    marginRight: 24,
    position: 'relative',
  },
  activeTabItem: {
    // Style cho tab active
  },
  tabText: {
    ...TypoSkin.label4,
  },
  activeTabText: {
    color: ColorThemes.light.Primary_Color_Main,
  },
  indicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 1.5,
    backgroundColor: ColorThemes.light.Primary_Color_Main,
  },
  safeAreaTop: {
    height: 16,
  },
  headerListTile: {
    borderRadius: 0,
  },
  backButton: {
    padding: 12,
    marginLeft: -8,
  },
  headerTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  headerSubtitle: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  shareButton: {
    padding: 4,
  },
  imageContainer: {
    height: 248,
    width: '100%',
  },
  backButtonOriginal: {
    position: 'absolute',
    top: 16,
    left: 16,
    zIndex: 111,
    paddingHorizontal: 12,
    borderRadius: 100,
    width: 40,
    height: 40,
  },
  shareButtonOriginal: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 111,
    paddingHorizontal: 12,
    borderRadius: 100,
    width: 40,
    height: 40,
  },
  editButton: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    zIndex: 111,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  coverImage: {
    height: 248,
    width: '100%',
    objectFit: 'cover',
  },
  groupTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  groupSubtitle: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  groupBottomContainer: {
    flex: 1,
    width: '100%',
    paddingTop: 20,
    gap: 8,
  },
  membersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 0,
  },
  memberItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  memberAvatar: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Disable,
    height: 24,
    width: 24,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 100,
  },
  avatarImage: {
    height: 24,
    width: 24,
    borderRadius: 100,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  moreButton: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Disable,
    height: 24,
    width: 24,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 100,
  },
  buttonsContainer: {
    paddingVertical: 16,
    flexDirection: 'row',
    width: '100%',
    gap: 8,
  },
  followButton: {
    flex: 1,
    borderRadius: 8,
  },
  inviteButton: {
    flex: 1,
    borderRadius: 8,
  },
  closeButton: {
    padding: 6,
    alignItems: 'center',
  },
  tabContent: {
    flex: 1,
    paddingTop: 16,
  },
  inviteContainer: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    minHeight: 600,
    height: 700,
    width: '100%',
  },
  searchContainer: {
    padding: 16,
    height: 56,
  },
  searchInput: {
    paddingHorizontal: 16,
    flex: 1,
    height: 40,
  },
  inviteListContainer: {
    flex: 1,
  },
  flatList: {
    flex: 1,
  },
  inviteSkeletonContainer: {
    flex: 1,
    backgroundColor: '#fff',
    gap: 16,
  },
  skeletonContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  inviteListTile: {
    gap: 8,
  },
  profileAvatar: {
    width: 48,
    height: 48,
    borderRadius: 50,
    backgroundColor: '#f0f0f0',
  },
  profileName: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  profileSubtitle: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  trailingContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  sentText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  joinedText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  inviteButtonContainer: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  inviteActionButton: {
    borderRadius: 8,
    paddingHorizontal: 8,
  },
  loadingMore: {
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerSpace: {
    height: 16,
  },
  skeletonHeader: {
    height: 400,
    width: '100%',
  },
  skeletonHeaderTop: {
    height: 56,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  skeletonBackButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  skeletonCover: {
    height: 248,
    width: '100%',
  },
  skeletonGroupInfo: {
    padding: 16,
  },
  skeletonTitle: {
    width: 200,
    height: 24,
    borderRadius: 4,
    marginBottom: 8,
  },
  skeletonSubtitle: {
    width: 120,
    height: 16,
    borderRadius: 4,
    marginBottom: 16,
  },
  skeletonMembersRow: {
    flexDirection: 'row',
  },
  skeletonMemberAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  skeletonButtonsRow: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 16,
  },
  skeletonButton: {
    flex: 1,
    height: 40,
    borderRadius: 8,
  },
  skeletonTabBar: {
    height: 50,
    flexDirection: 'row',
    paddingHorizontal: 20,
  },
  skeletonTab: {
    width: 100,
    height: 20,
    borderRadius: 4,
    marginRight: 24,
  },
  skeletonContent: {
    padding: 16,
    gap: 16,
  },
  skeletonPostInput: {
    height: 69,
    padding: 16,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  skeletonInputAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  skeletonInputField: {
    flex: 1,
    height: 40,
    borderRadius: 8,
  },
  skeletonPost: {
    padding: 16,
    borderRadius: 8,
    gap: 12,
  },
});

// Component SkeletonGroupIndex để hiển thị trong trạng thái loading
function SkeletonGroupIndex() {
  return (
    <View style={styles.skeletonContainer}>
      {/* Skeleton cho header */}
      <SkeletonPlaceholder
        backgroundColor="#f0f0f0"
        highlightColor="#e0e0e0"
        speed={800}>
        <View style={styles.skeletonHeader}>
          {/* Header với nút back */}
          <View style={styles.skeletonHeaderTop}>
            <View style={styles.skeletonBackButton} />
          </View>

          {/* Ảnh cover */}
          <View style={styles.skeletonCover} />

          {/* Thông tin nhóm */}
          <View style={styles.skeletonGroupInfo}>
            <View style={styles.skeletonTitle} />
            <View style={styles.skeletonSubtitle} />

            {/* Avatar members */}
            <View style={styles.skeletonMembersRow}>
              {[1, 2, 3].map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.skeletonMemberAvatar,
                    {marginLeft: index > 0 ? -10 : 0},
                  ]}
                />
              ))}
            </View>

            {/* Buttons */}
            <View style={styles.skeletonButtonsRow}>
              <View style={styles.skeletonButton} />
              <View style={styles.skeletonButton} />
            </View>
          </View>
        </View>
      </SkeletonPlaceholder>

      {/* Skeleton cho tab bar */}
      <SkeletonPlaceholder
        backgroundColor="#f0f0f0"
        highlightColor="#e0e0e0"
        speed={800}>
        <View style={styles.skeletonTabBar}>
          <View style={styles.skeletonTab} />
          <View style={styles.skeletonTab} />
        </View>
      </SkeletonPlaceholder>

      {/* Skeleton cho nội dung */}
      <SkeletonPlaceholder
        backgroundColor="#f0f0f0"
        highlightColor="#e0e0e0"
        speed={800}>
        <View style={styles.skeletonContent}>
          {/* Post input */}
          <View style={styles.skeletonPostInput}>
            <View style={styles.skeletonInputAvatar} />
            <View style={styles.skeletonInputField} />
          </View>

          {/* Posts */}
          {[1, 2, 3].map((_, index) => (
            <View key={index} style={styles.skeletonPost}>
              <SkeletonPlaceCard />
            </View>
          ))}
        </View>
      </SkeletonPlaceholder>
    </View>
  );
}
