/* eslint-disable react-native/no-inline-styles */
import React, {useState, useCallback, useMemo, memo} from 'react';
import {Dimensions, Text, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {ListTile} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';
// Sử dụng component YouTubePlayer tùy chỉnh thay vì component mặc định
import YouTubePlayer from '../../../utils/YouTubeWebViewPlayer';

// Import từ DefaultPostModule
import {
  DefaultPostProps,
  ImageData,
  extractVideoId,
  stylesDefault,
  SingleImage,
  TwoImages,
  ThreeImages,
  MultipleImages,
  HTMLContent,
  renderListItems,
  renderSeeMoreButton,
  renderTags,
} from './DefaultPostModule';
import ContentBackground from './DefaultPostModule/ContentBackground';

// Re-export SkeletonPlacePostCard
export {SkeletonPlacePostCard} from './DefaultPostModule';

// Tối ưu component chính với React.memo
export const DefaultPost = memo((props: DefaultPostProps) => {
  const [isContentExpanded, setIsContentExpanded] = useState(false);
  const contentWidth = useMemo(() => Dimensions.get('window').width - 32, []);

  // Tối ưu xử lý ảnh với useMemo
  const imageData = useMemo<ImageData>(() => {
    if (!props?.data?.Img) return {hasImages: false, imageUrls: [], count: 0};

    const imgStr = props.data.Img;
    if (imgStr.includes(',')) {
      const imgArray = imgStr
        .split(',')
        .filter((img: any) => img && img.trim() !== '')
        .slice(0, 10);

      if (imgArray.length === 0)
        return {hasImages: false, imageUrls: [], count: 0};

      return {
        hasImages: true,
        isMultiple: true,
        imageUrls: imgArray.map((img: any) => ConfigAPI.getValidLink(img)),
        count: imgArray.length,
      };
    } else {
      return {
        hasImages: true,
        isMultiple: false,
        imageUrls: [ConfigAPI.getValidLink(imgStr)],
        count: 1,
      };
    }
  }, [props?.data?.Img]);

  // Tối ưu render ảnh với useMemo
  const renderedImages = useMemo(() => {
    if (!imageData.hasImages) return null;

    if (!imageData.isMultiple || imageData.count === 1) {
      return <SingleImage imageUrl={imageData.imageUrls[0]} />;
    }

    switch (imageData.count) {
      case 2:
        return <TwoImages imageUrls={imageData.imageUrls} />;
      case 3:
        return <ThreeImages imageUrls={imageData.imageUrls} />;
      default:
        return (
          <MultipleImages
            imageUrls={imageData.imageUrls}
            imageCount={imageData.count}
          />
        );
    }
  }, [imageData]);

  // Sử dụng useCallback để tránh tạo lại hàm mỗi khi render
  const toggleContentExpand = useCallback(() => {
    setIsContentExpanded(prev => !prev);
  }, []);

  // Tạo memoized user image uri
  const userImageUri = useMemo(() => {
    return props.data?.relativeUser?.image
      ? ConfigAPI.getValidLink(props.data?.relativeUser?.image)
      : undefined;
  }, [props.data?.relativeUser?.image]);

  const renderContentDefault = useMemo(() => {
    return (
      <View style={{flex: 1, marginTop: 4, gap: 8}}>
        <TouchableOpacity
          onPress={props.onPressDetail}
          disabled={!props.onPressDetail}
          style={{flex: 1}}>
          {/* HTML content */}
          <HTMLContent
            content={props.data?.Content || ''}
            contentWidth={contentWidth}
            isContentExpanded={isContentExpanded}
            onToggleExpand={toggleContentExpand}
          />
          {/* List items */}
          {renderListItems(props)}

          {/* See more button */}
          {renderSeeMoreButton(props)}

          {/* Tags */}
          {renderTags(props)}
        </TouchableOpacity>

        {/* Images */}
        {imageData.hasImages ? (
          <View style={[stylesDefault.img, {marginVertical: 16}]}>
            {renderedImages}
          </View>
        ) : null}

        {/* Video player - Tách ra khỏi TouchableOpacity để tránh trùng sự kiện */}
        {props.data?.LinkVideo ? (
          <View style={{marginBottom: 16}}>
            <YouTubePlayer
              videoId={extractVideoId(props.data?.LinkVideo) || ''}
              height={200}
              width={'100%'}
              play={false}
              useNativeControls={true}
              playerParams={{
                modestbranding: true,
              }}
            />
          </View>
        ) : null}
      </View>
    );
  }, [props.data?.Content]);

  const renderContentBackground = useMemo(() => {
    if (!props.data?.PostBackgroundM) return null;
    return (
      <TouchableOpacity
        style={{flex: 1}}
        onPress={props.onPressDetail}
        disabled={!props.onPressDetail}>
        <ContentBackground
          text={props.data?.Content || ''}
          background={props.data?.PostBackgroundM}
        />
      </TouchableOpacity>
    );
  }, [props.data?.Content, props.data?.PostBackgroundM]);

  return (
    <ListTile
      style={{flex: 1, height: '100%', borderRadius: 0}}
      leading={
        <FastImage
          key={userImageUri || 'no-image' + props.data?.relativeUser?.title}
          source={
            userImageUri
              ? {uri: userImageUri}
              : require('../../../assets/appstore.png')
          }
          style={{
            width: 48,
            height: 48,
            borderRadius: 50,
            backgroundColor: '#f0f0f0',
          }}
        />
      }
      title={
        <Text
          style={{
            ...TypoSkin.heading7,
            color: ColorThemes.light.Neutral_Text_Color_Title,
          }}
          numberOfLines={1}>
          {props.data?.relativeUser?.title ?? '-'}
        </Text>
      }
      subtitle={props.data?.relativeUser?.subtitle ?? '-'}
      subTitleStyle={{
        ...TypoSkin.subtitle3,
        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
      }}
      onPress={props.onPressHeader}
      disabled={!props.onPressHeader}
      trailing={props.trailingView}
      // content
      bottom={
        <View style={{flex: 1, width: '100%', paddingTop: 16}}>
          {props.data?.PostBackgroundM
            ? renderContentBackground
            : renderContentDefault}

          {/* actions */}
          {props.actionView && (
            <View style={{flexDirection: 'row'}}>{props.actionView}</View>
          )}
        </View>
      }
    />
  );
});
