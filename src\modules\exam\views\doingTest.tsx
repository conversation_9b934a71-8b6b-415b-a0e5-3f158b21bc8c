/* eslint-disable react-native/no-inline-styles */
import {
  ActivityIndicator,
  Dimensions,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../../Screen/Layout/header';
import {
  navigate,
  navigateBack,
  navigateReset,
  RootScreen,
} from '../../../router/router';
import {
  AppButton,
  Checkbox,
  FBottomSheet,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import WScreenFooter from '../../../Screen/Layout/footer';
import {TypoSkin} from '../../../assets/skin/typography';
import {useEffect, useRef, useState} from 'react';
import {RadioButton} from 'react-native-paper';
import PagerView from 'react-native-pager-view';
import CountdownTimer from '../components/countDownTimer';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../redux/store/store';
import {useExamData} from '../../../redux/hook/examHook';
import {useNavigation, useRoute} from '@react-navigation/native';
import {ExamActions} from '../../../redux/reducers/examReducer';
import {randomGID} from '../../../utils/Utils';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {
  ExamType,
  Sakupi,
  SakupiType,
  StatusExam,
  StorageContanst,
} from '../../../Config/Contanst';
import {LessonActions} from '../../../redux/reducers/proccessLessonReducer';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import RenderHTML from 'react-native-render-html';
import Sound from 'react-native-sound';
import ClickableImage from '../components/ClickableImage';
import ConfigAPI from '../../../Config/ConfigAPI';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {getSafeHtmlContent} from './doingTestNewRedesigned';

export default function DoingTest() {
  const {t} = useTranslation();
  const pagerRef = useRef<PagerView>(null); // Ref for PagerView
  const [currentPage, setCurrentPage] = useState(0); // Track current page
  const dateStart = new Date().getTime();
  const maxProgressRef = useRef(0);
  const navigation = useNavigation<any>();
  const next = () => {
    if (pagerRef.current) {
      pagerRef.current.setPage(currentPage + 1); // Navigate to the next page
      setCurrentPage(currentPage + 1);
    }
  };

  const previous = () => {
    if (pagerRef.current) {
      pagerRef.current.setPage(currentPage - 1); // Navigate to the previous page
      setCurrentPage(currentPage - 1);
    }
  };
  const onSubmit = async (dateEnd: number, dateStart: number, id: string) => {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    var score = 0;
    var totalS = 0;
    var lstDetail: any[] = [];
    var lstDetailAnswer: any[] = [];
    var idTest = randomGID();
    var correctAnswer = 0;
    listQuestion?.map((item, index) => {
      totalS += item.Score;
      var isResultDetail = false;
      var IdAnswer;
      var idsAnser = item.lstAnswer.filter((tp: any) => tp.choose === true);

      if (item.SelectionType === 1) {
        // tính điểm với câu hỏi chọn 1
        IdAnswer = idsAnser[0]?.Id;
        if (
          item.lstAnswer.some(
            (a: any) => a.choose === true && a.IsResult === true,
          )
        ) {
          score += item.Score ?? 1;
          correctAnswer++;
          isResultDetail = true;
        }
      } else {
        // tính điểm với câu hỏi chọn nhiều - Lấy hết đáp án cần phải tích đúng. kiểm tra xem user chọn hết các đáp án đấy k
        if (idsAnser?.length > 0) {
          IdAnswer = idsAnser?.map((u: any) => u.Id).join(',');
        }
        var wrongChoices = item.lstAnswer.filter(
          (i: any) => i.IsResult !== true && i.choose === true,
        );
        var correctAnswers = item.lstAnswer.filter(
          (i: any) => i.IsResult === true,
        );
        if (
          !correctAnswers.some((a: any) => a.choose !== true) &&
          wrongChoices.length === 0
        ) {
          score += item.Score ?? 1;
          correctAnswer++;
          isResultDetail = true;
        }
      }
      // lưu lịch sử các câu hỏi/
      var IdDetail = randomGID();
      lstDetail.push({
        Id: IdDetail,
        QuestionId: item.Id,
        Sort: index + 1,
        AnswerId: IdAnswer,
        TestId: idTest,
        SelectionType: item.SelectionType,
        IsResult: isResultDetail,
        DateCreated: new Date().getTime(),
        QuestionContent: item.Content,
      });
      // lưu lịch sử các đáp án trên từng câu hỏi/
      item.lstAnswer.map((answer: any) => {
        lstDetailAnswer.push({
          Id: randomGID(),
          Name: answer.Content,
          DateCreated: new Date().getTime(),
          IsResult: answer.IsResult ?? false,
          QuestionId: item.Id,
          Test_ResultId: IdDetail,
          AnswerId: answer.Id,
        });
      });
    });
    const data = {
      Id: idTest,
      DateCreated: new Date().getTime(),
      DateEnd: dateEnd,
      DateStart: dateStart,
      Type: examInfor.Type,
      ExamId: id,
      Name: examInfor.Name,
      CustomerId: cusId,
      Time: examInfor.Time,
      Score: score ?? 0,
      Status:
        (score / totalS) * 100 >= examInfor.PassingScore
          ? StatusExam.passed
          : StatusExam.fail,
      TotalAnswerCorrect: correctAnswer,
      TotalQuestion: listQuestion.length ?? 0,
    };
    if ((score / totalS) * 100 >= examInfor.PassingScore) {
      await dispatch(
        LessonActions.updateProcess({
          Id: Step?.Id,
          CourseId: Step?.CourseId,
          lessonId: Step?.Id,
          stepOrder: Step?.StepOrder,
          PartId: Step?.PartId,
          PercentCompleted: 100,
          Type: 'Exam',
        }),
      );
    }
    await dispatch(ExamActions.examSubmit(data)).then(() => {
      navigation.replace(RootScreen.resultTest, {
        id: Step?.id,
        courseId: Step?.courseId,
        type: type,
      });
    });
  };
  const dispatch: AppDispatch = useDispatch();
  const route = useRoute<any>();
  const {id, type, Step} = route.params;
  const {examInfor} = useExamData();
  const {loading} = useExamData();
  const {listQuestion} = useExamData();
  useEffect(() => {
    maxProgressRef.current = 0;
    setCurrentPage(0);
  }, [Step?.stepId]);
  const bottomSheetRef = useRef<any>(null);
  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <ScreenHeader
        action={
          <AppButton
            title={t('exam.submit')}
            containerStyle={{
              justifyContent: 'flex-start',
              alignSelf: 'baseline',
              paddingRight: 16,
            }}
            backgroundColor={'transparent'}
            textStyle={TypoSkin.buttonText3}
            borderColor="transparent"
            onPress={() => {
              showBottomSheet({
                ref: bottomSheetRef,
                enableDismiss: true,
                title: t('exam.confirmSubmit'),
                prefixAction: <View />,
                suffixAction: (
                  <TouchableOpacity
                    onPress={() => hideBottomSheet(bottomSheetRef)}
                    style={{padding: 6, alignItems: 'center'}}>
                    <Winicon
                      src="outline/layout/xmark"
                      size={20}
                      color={ColorThemes.light.Neutral_Text_Color_Body}
                    />
                  </TouchableOpacity>
                ),
                children: (
                  <ConFirmEndTest
                    ref={bottomSheetRef}
                    dateStart={dateStart}
                    dateEnd={dateStart + examInfor?.Time * 60 * 1000}
                    id={examInfor?.Id}
                    courseId={id}
                    type={type}
                    step={Step}
                  />
                ),
              });
            }}
            textColor={ColorThemes.light.Info_Color_Main}
          />
        }
        title={
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <CountdownTimer
              textStyle={{
                ...TypoSkin.title3,
                fontWeight: '700',
                color: ColorThemes.light.Neutral_Text_Color_Title,
              }}
              initialMinutes={examInfor?.Time ?? 0}
              onTimeUp={() => {
                onSubmit(
                  dateStart + examInfor.Time * 60 * 1000,
                  dateStart,
                  examInfor.Id,
                );
              }}
            />
            <Text
              style={{
                ...TypoSkin.subtitle3,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              }}>
              {`${currentPage + 1}/${listQuestion?.length ?? 0}`}
            </Text>
          </View>
        }
        backIcon={<Winicon src="outline/user interface/e-remove" size={20} />}
        onBack={() => {
          showBottomSheet({
            ref: bottomSheetRef,
            enableDismiss: true,
            title: t('exam.confirmExit'),
            prefixAction: <View />,
            suffixAction: (
              <TouchableOpacity
                onPress={() => hideBottomSheet(bottomSheetRef)}
                style={{padding: 6, alignItems: 'center'}}>
                <Winicon
                  src="outline/layout/xmark"
                  size={20}
                  color={ColorThemes.light.Neutral_Text_Color_Body}
                />
              </TouchableOpacity>
            ),
            children: <ConFirmCancel ref={bottomSheetRef} />,
          });
        }}
      />
      {/* checklist */}
      <AppButton
        containerStyle={{
          width: 60,
          height: 60,
          borderRadius: 30,
          justifyContent: 'center',
          alignItems: 'center',
          position: 'absolute',
          zIndex: 1,
          bottom: 90,
          right: 16,
        }}
        backgroundColor={ColorThemes.light.Primary_Color_Main}
        borderColor="transparent"
        title={
          <Winicon
            src="outline/text/ordered-list"
            size={24}
            color={ColorThemes.light.Neutral_Background_Color_Absolute}
          />
        }
        onPress={async () => {
          showBottomSheet({
            ref: bottomSheetRef,
            enableDismiss: true,
            title: t('exam.selectQuestion'),
            prefixAction: <View />,
            suffixAction: (
              <TouchableOpacity
                onPress={() => hideBottomSheet(bottomSheetRef)}
                style={{padding: 6, alignItems: 'center'}}>
                <Winicon
                  src="outline/layout/xmark"
                  size={listQuestion?.length ?? 1}
                />
              </TouchableOpacity>
            ),
            children: (
              <Pressable
                style={{
                  height: Dimensions.get('screen').height / 2,
                  width: '100%',
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  gap: 16,
                  paddingHorizontal: 16,
                  paddingTop: 16,
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Absolute,
                }}>
                {listQuestion?.map((item, index) => (
                  <AppButton
                    key={item.Id}
                    containerStyle={{
                      width: 32,
                      height: 32,
                      borderRadius: 30,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    backgroundColor={
                      item.lstAnswer.some((a: any) => a.choose)
                        ? ColorThemes.light.Success_Color_Background
                        : ColorThemes.light.Neutral_Background_Color_Main
                    }
                    onPress={() => {
                      if (pagerRef.current) {
                        pagerRef.current.setPage(index); // Navigate to the page
                        setCurrentPage(index);
                        hideBottomSheet(bottomSheetRef);
                      }
                    }}
                    borderColor="transparent"
                    title={`${index + 1}`}
                    textColor={
                      item.lstAnswer.some((a: any) => a.choose)
                        ? ColorThemes.light.Success_Color_Main
                        : ColorThemes.light.Neutral_Text_Color_Body
                    }
                  />
                ))}
              </Pressable>
            ),
          });
        }}
      />
      {/* content */}
      {loading ? (
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
          }}>
          <ActivityIndicator color={ColorThemes.light.Primary_Color_Main} />
        </View>
      ) : (
        <ContentTest ref={pagerRef} />
      )}

      {/*  */}
      <WScreenFooter
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          alignContent: 'center',
          paddingHorizontal: 16,
        }}>
        {currentPage === 0 ? (
          <View />
        ) : (
          <AppButton
            title={t('exam.previousQuestion')}
            containerStyle={{}}
            backgroundColor={'transparent'}
            textStyle={TypoSkin.buttonText3}
            borderColor="transparent"
            prefixIconSize={16}
            prefixIcon={'outline/arrows/left-arrow'}
            onPress={previous} // Call previous function
            textColor={ColorThemes.light.Info_Color_Main}
          />
        )}
        {currentPage === listQuestion.length - 1 ? (
          <View />
        ) : (
          <AppButton
            title={t('exam.nextQuestion')}
            containerStyle={{}}
            backgroundColor={'transparent'}
            textStyle={TypoSkin.buttonText3}
            borderColor="transparent"
            suffixIconSize={16}
            suffixIcon={'outline/arrows/right-arrow'}
            onPress={async () => {
              next();
            }} // Call next function
            textColor={ColorThemes.light.Info_Color_Main}
          />
        )}
      </WScreenFooter>
    </SafeAreaView>
  );
}

const ContentTest = ({ref}: any) => {
  const {t} = useTranslation();
  const dispatch: AppDispatch = useDispatch();
  const route = useRoute<any>();
  const {id} = route.params;
  const {listQuestion} = useExamData();

  // Audio state management
  const [playedAudios, setPlayedAudios] = useState<{
    [questionId: string]: boolean;
  }>({});
  const soundRef = useRef<Sound | null>(null);

  // Function to play audio
  const playAudio = (audioUrl: string, questionId: string) => {
    // Stop any currently playing sound
    stopAudio();

    // Set up sound
    Sound.setCategory('Playback');
    // Create new sound instance
    const sound = new Sound(audioUrl, '', error => {
      if (error) {
        console.log('Failed to load sound', error);
        return;
      }

      // Play the sound
      sound.play(success => {
        if (success) {
          console.log('Sound played successfully');
          // Mark this audio as played
          setPlayedAudios(prev => ({
            ...prev,
            [questionId]: true,
          }));
        } else {
          console.log('Sound playback failed');
        }
      });
    });

    // Save reference to sound
    soundRef.current = sound;
  };

  // Function to stop audio
  const stopAudio = () => {
    if (soundRef.current) {
      soundRef.current.stop();
      soundRef.current.release();
      soundRef.current = null;
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopAudio();
    };
  }, []);
  return (
    <View style={{flex: 1}}>
      <PagerView
        ref={ref}
        style={{height: '100%'}}
        initialPage={0}
        scrollEnabled={false}>
        {listQuestion?.map((item: any, index: number) => (
          <View key={index}>
            {/* content */}
            <ScrollView style={{flex: 1}}>
              {/* question */}
              <ListTile
                style={{paddingHorizontal: 16, padding: 0, paddingTop: 16}}
                // title={`${index + 1}: ${item.Name}`}
                title={
                  <RenderHTML
                    contentWidth={Dimensions.get('window').width - 32}
                    source={{
                      html: getSafeHtmlContent(
                        item.Title || item.Name || item.Content,
                        '<p>No content available</p>',
                      ),
                    }}
                    tagsStyles={{
                      body: {
                        fontSize: 16,
                        lineHeight: 24,
                        fontFamily: 'Inter',
                      },
                      p: {
                        fontSize: 16,
                        lineHeight: 24,
                        fontFamily: 'Inter',
                      },
                      img: {
                        marginVertical: 10,
                        alignSelf: 'center',
                        borderRadius: 8,
                        borderWidth: 1,
                        maxWidth: '100%',
                        height: 200,
                      },
                    }}
                    renderers={{
                      img: ({TDefaultRenderer, ...props}: any) => {
                        const {src} = props.tnode.attributes;
                        if (src) {
                          return (
                            <ClickableImage
                              source={{uri: src}}
                              style={{
                                marginVertical: 10,
                                alignSelf: 'center',
                                borderRadius: 8,
                                borderWidth: 1,
                                maxWidth: '100%',
                                height: 200,
                              }}
                              resizeMode="contain"
                            />
                          );
                        }
                        return <TDefaultRenderer {...props} />;
                      },
                    }}
                  />
                }
              />
              {item.Audio && !playedAudios[item.Id] && (
                <View
                  style={{
                    marginHorizontal: 16,
                    backgroundColor:
                      ColorThemes.light.neutral_main_background_color,
                    borderRadius: 8,
                  }}>
                  <TouchableOpacity
                    disabled={playedAudios[item.Id]}
                    onPress={() => {
                      const audioUrl = ConfigAPI.getValidLink(item.Audio);
                      playAudio(audioUrl, item.Id);
                    }}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <Winicon
                      src={
                        playedAudios[item.Id]
                          ? 'color/multimedia/button-pause'
                          : 'outline/multimedia/sound'
                      }
                      size={16}
                      color={
                        playedAudios[item.Id]
                          ? ColorThemes.light.Neutral_Text_Color_Subtitle
                          : ColorThemes.light.Info_Color_Main
                      }
                    />
                    <Text
                      style={{
                        ...TypoSkin.body3,
                        color: playedAudios[item.Id]
                          ? ColorThemes.light.Neutral_Text_Color_Subtitle
                          : ColorThemes.light.Info_Color_Main,
                        marginLeft: 8,
                        fontWeight: '600',
                      }}>
                      {playedAudios[item.Id] ? 'Đã nghe' : 'Bấm để nghe'}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
              <ListTile
                style={{
                  paddingHorizontal: 16,
                  padding: 0,
                  paddingBottom: 16,
                  paddingTop: 8,
                }}
                title={
                  item.SelectionType === 2
                    ? t('exam.selectMultipleAnswers')
                    : t('exam.selectOneAnswer')
                }
                titleStyle={{
                  ...TypoSkin.body2,
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                  fontSize: 12,
                }}
              />

              {/* Question Image */}
              {item.Img && (
                <View
                  style={{
                    marginHorizontal: 16,
                    marginBottom: 16,
                    borderRadius: 8,
                    overflow: 'hidden',
                    backgroundColor:
                      ColorThemes.light.neutral_main_background_color,
                  }}>
                  <ClickableImage
                    source={{
                      uri: item.Img?.includes('http')
                        ? item.Img
                        : ConfigAPI.getValidLink(item.Img),
                    }}
                    style={{
                      width: '100%',
                      height: 200,
                      borderRadius: 8,
                    }}
                    resizeMode="contain"
                  />
                </View>
              )}

              {/* Question Audio */}

              {/* answers */}
              <View style={{gap: 8, paddingBottom: 150}}>
                {item.lstAnswer?.map((answer: any, index: number) => {
                  return (
                    <ListTile
                      key={index}
                      onPress={() => {
                        dispatch(ExamActions.choose(item.Id, answer.Id));
                      }}
                      leading={
                        item.SelectionType === 2 ? (
                          <Checkbox
                            key={index}
                            value={answer.choose ?? false}
                            onChange={() => {
                              dispatch(ExamActions.choose(item.Id, answer.Id));
                            }}
                            checkboxStyle={{
                              backgroundColor:
                                ColorThemes.light.Info_Color_Main,
                            }}
                          />
                        ) : (
                          <RadioButton.Android
                            key={index}
                            value={answer.Id}
                            status={answer.choose ? 'checked' : 'unchecked'}
                            color={ColorThemes.light.Info_Color_Main}
                            onPress={() => {
                              dispatch(ExamActions.choose(item.Id, answer.Id));
                            }}
                          />
                        )
                      }
                      listtileStyle={{gap: 12, padding: 8}}
                      title={`${index + 1}: ${answer.Name ?? ''}`}
                      style={{
                        borderColor: answer.choose
                          ? ColorThemes.light.Info_Color_Main
                          : ColorThemes.light.Neutral_Border_Color_Main,
                        borderWidth: 1,
                        marginHorizontal: 16,
                        padding: 0,
                      }}
                      titleStyle={{
                        ...TypoSkin.body2,
                        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}
                    />
                  );
                })}
              </View>
            </ScrollView>
          </View>
        ))}
      </PagerView>
    </View>
  );
};

const ConFirmEndTest = (pros: any) => {
  const {t} = useTranslation();
  const dispatch: AppDispatch = useDispatch();
  const {listQuestion, examInfor} = useExamData();
  const navigation = useNavigation<any>();
  useEffect(() => {}, []);
  return (
    <View
      style={{
        height: 150,
        width: '100%',
        gap: 16,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        paddingHorizontal: 16,
      }}>
      <AppButton
        title={t('exam.submitNow')}
        backgroundColor={ColorThemes.light.Primary_Color_Main}
        borderColor="transparent"
        containerStyle={{
          height: 45,
          marginTop: 16,
          borderRadius: 8,
        }}
        onPress={async () => {
          const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
          var score = 0;
          var totalS = 0;
          // var lstDetail: any[] = [];
          // var lstDetailAnswer: any[] = [];
          var questionIds: any[] = [];
          var answerIds: any[] = [];
          var idTest = randomGID();
          var correctAnswer = 0;
          //check xem người dùng có trả lời câu nào ko
          const listCheck = listQuestion.filter((item: any) =>
            item.lstAnswer.some((a: any) => a.choose === true),
          );

          if (listCheck.length === 0) {
            navigation.replace(RootScreen.resultTest, {
              id: pros?.id,
              courseId: pros.step?.courseId,
              type: pros?.type,
              isCheck: true,
            });
          } else {
            listQuestion?.map((item, index) => {
              totalS += item.Score;
              var isResultDetail = false;
              var IdAnswer;
              var idsAnser = item.lstAnswer.filter(
                (tp: any) => tp.choose === true,
              );

              if (item.SelectionType === 1) {
                // tính điểm với câu hỏi chọn 1
                IdAnswer = idsAnser[0]?.Id;
                if (
                  item.lstAnswer.some(
                    (a: any) => a.choose === true && a.IsResult === true,
                  )
                ) {
                  score += item.Score ?? 1;
                  correctAnswer++;
                  isResultDetail = true;
                }
              } else {
                // tính điểm với câu hỏi chọn nhiều - Lấy hết đáp án cần phải tích đúng. kiểm tra xem user chọn hết các đáp án đấy k
                if (idsAnser?.length > 0) {
                  IdAnswer = idsAnser?.map((u: any) => u.Id).join(',');
                }
                var wrongChoices = item.lstAnswer.filter(
                  (i: any) => i.IsResult !== true && i.choose === true,
                );
                var correctAnswers = item.lstAnswer.filter(
                  (i: any) => i.IsResult === true,
                );

                if (
                  !correctAnswers.some((a: any) => a.choose !== true) &&
                  wrongChoices.length === 0
                ) {
                  score += item.Score ?? 1;
                  correctAnswer++;
                  isResultDetail = true;
                }
              }
              questionIds.push(item.Id);
              answerIds.push(IdAnswer);
              // lưu lịch sử các câu hỏi/
              // var IdDetail = randomGID();
              // lstDetail.push({
              //   Id: IdDetail,
              //   QuestionId: item.Id,
              //   Sort: index + 1,
              //   AnswerId: IdAnswer,
              //   SelectionType: item.SelectionType,
              //   TestId: idTest,
              //   IsResult: isResultDetail,
              //   DateCreated: new Date().getTime(),
              //   QuestionContent: item.Content,
              // });
              // lưu lịch sử các đáp án trên từng câu hỏi/
              // item.lstAnswer.map((answer: any) => {
              //   lstDetailAnswer.push({
              //     Id: randomGID(),
              //     Name: answer.Content,
              //     DateCreated: new Date().getTime(),
              //     IsResult: answer.IsResult ?? false,
              //     QuestionId: item.Id,
              //     Test_ResultId: IdDetail,
              //     AnswerId: answer.Id,
              //   });
              // });
            });
            //loại bỏ các đáp án null
            answerIds = answerIds.filter(Boolean);
            const data = {
              Id: idTest,
              DateCreated: new Date().getTime(),
              DateEnd: pros.dateEnd,
              DateStart: pros.dateStart,
              // ExamId: pros.id,
              Type: examInfor.Type,
              Name: examInfor.Name,
              CustomerId: cusId,
              // Time: examInfor.Time,
              Score: score ?? 0,
              Status:
                score >= examInfor.PassingScore
                  ? StatusExam.passed
                  : StatusExam.fail,
              // TotalAnswerCorrect: correctAnswer,
              // TotalQuestion: listQuestion.length ?? 0,
              QuestionIds: questionIds?.join(','),
              AnswerIds: answerIds.join(','),
              LessonId: pros.step.lessonId,
              ExamId: pros.id,
            };
            if (score >= examInfor.PassingScore) {
              dispatch(
                LessonActions.updateProcess({
                  Id: pros.step?.lessonId,
                  CourseId: pros.step?.CourseId,
                  lessonId: pros.step?.lessonId,
                  stepOrder: pros.step?.StepOrder,
                  PartId: pros.step?.PartId,
                  PercentCompleted: 100,
                  Type: 'Exam',
                }),
              );
              dispatch(
                CustomerActions.updateRank(SakupiType.exam, Sakupi.exam),
              );
            }
            hideBottomSheet(pros.ref);
            await dispatch(ExamActions.examSubmit(data)).then(() => {
              navigation.replace(RootScreen.resultTest, {
                id: pros?.id,
                courseId: pros.step?.courseId,
                type: pros?.type,
              });
            });
          }
        }}
        textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
      />
      <AppButton
        title={t('exam.continueExam')}
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        containerStyle={{
          height: 45,
          borderRadius: 8,
        }}
        onPress={() => {
          hideBottomSheet(pros.ref);
        }}
        textColor={ColorThemes.light.Neutral_Text_Color_Body}
      />
    </View>
  );
};
const ConFirmCancel = ({ref}: {ref: any}) => {
  const {t} = useTranslation();
  const dispatch: AppDispatch = useDispatch();
  return (
    <View
      style={{
        height: 180,
        width: '100%',
        gap: 16,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        paddingHorizontal: 16,
      }}>
      <Text
        style={{
          ...TypoSkin.body3,
          color: ColorThemes.light.Neutral_Text_Color_Subtitle,
          textAlign: 'center',
        }}>
        {t('exam.exitWarning')}
      </Text>
      <AppButton
        title={t('exam.exit')}
        backgroundColor={ColorThemes.light.Error_Color_Main}
        borderColor="transparent"
        containerStyle={{
          height: 45,

          borderRadius: 8,
        }}
        onPress={() => {
          dispatch(ExamActions.resetReducer());
          hideBottomSheet(ref);
          navigateBack();
        }}
        textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
      />
      <AppButton
        title={t('exam.continueExam')}
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        containerStyle={{
          height: 45,
          borderRadius: 8,
        }}
        onPress={() => {
          hideBottomSheet(ref);
        }}
        textColor={ColorThemes.light.Neutral_Text_Color_Body}
      />
    </View>
  );
};
