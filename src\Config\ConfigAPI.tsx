const hostMapUrl = 'https://server.wini.vn/api/data/';

export default class ConfigAPI {
  static regexGuid = /^[0-9a-fA-F]{32}$/;

  static getValidLink = (link: string) => {
    if (!link || link === '' || link === null) return '';
    if (link.startsWith('http') || link.startsWith('https')) return link;
    else {
      var tmp = link.split('/');
      var type = 0;
      if (this.regexGuid.test(tmp[0])) type = 1;
      else {
        tmp.forEach((item: any, index: number) => {
          if (this.regexGuid.test(item)) {
            type = 2;
          }
        });
      }
      if (type === 1) {
        return this.urlImg + link;
      } else if (type === 2) {
        return 'https://dev.wini.vn/' + link;
      } else {
        return this.fileUrl + link;
      }

    };
    // else return ConfigAPI.fileUrl + link;
  };

  static url = 'https://dev.wini.vn/api/';
  static urlWeb = 'https://eschool.itm.vn';
  static pid = 'f5e4a5074091423981f047cf9f883175';
  static googleApiKey = 'AIzaSyBrjZpmgCpST9GWPt7fCnr_EiQi-uL9SQM';
  static fileUrl = 'https://cdneschool.itm.vn/uploads/';
  static urlImg = 'https://dev.wini.vn/api/file/img/';
  static adminITM = 'ddb8e94bb5b44fe4bccb8b59976f58bc';
  static gameALTP = 'cf86bc33ef03447fa744eea2bbf31cfc';
  static gameSakuTB = '7eb45b0edc6247c3bde6bbb15547dfda'; // TODO: Replace with actual game ID
  static gameDHBC = '1d56852db9964d9a878a1d9d5f872cb7'; // Game DHBC ID
  static gameMGHH = '1b1804be1c6049c2876d1626794fa7a0'; // Game MGHH ID
  static gameSakuLC = '769bce29753d4fa48314325c4bc7ebb0'; // Game Saku LC ID
  static gameSKXT = '19d8c9d61ae74f968416b28fcf8e93c3'; // Game Saku Xay To ID
  static gameSakusanmoi = '05ac80f4e3b54615afb06d49f5140ade'; // Game Saku San moi ID
  static gameVTNV = 'e856cf006f2745bda1b21dba65df4d71'; // Game VTNV ID
  static gameSakuTC = 'c0b7fbb981bf495295de84d6b6b008c7'; // Game TC ID

  static GEOCODING_API_URL_BY_GOOGLE = (lat: any, lng: any) => {
    // return `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${ConfigAPI.googleApiKey}`
    return (
      hostMapUrl +
      `geocode/json?latlng=${lat},${lng}&key=${ConfigAPI.googleApiKey}`
    );
  };

  static getAddressByGoogleKey = (inputText: string) => {
    // console.log('====================================');
    // console.log(hostMapUrl + `place/textsearch/json?&query=${encodeURIComponent(inputText)}&components=country:VN&key=${ConfigAPI.googleApiKey}`);
    // console.log('====================================');
    return (
      hostMapUrl +
      `place/textsearch/json?&query=${encodeURIComponent(
        inputText,
      )}&components=country:VN&key=${ConfigAPI.googleApiKey}`
    );
  };

  static provinceUrl = 'https://esgoo.net/api-tinhthanh/1/0.htm';
  static districtUrl = (cityId: string) =>
    `https://esgoo.net/api-tinhthanh/2/${cityId}.htm`;

  static wardUrl = (districtId: string) =>
    `https://esgoo.net/api-tinhthanh/3/${districtId}.htm`;
}
