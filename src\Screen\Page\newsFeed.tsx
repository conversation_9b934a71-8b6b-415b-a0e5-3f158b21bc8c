/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../assets/skin/colors';
import TitleWithBottom from '../Layout/titleWithBottom';
import ByNewCategory from '../../modules/community/news/listview/byNewCates';
import ByNewTrending from '../../modules/community/news/listview/byTrending';

import {DrawerActions, useNavigation} from '@react-navigation/native';
import {HashTag, ListTile} from 'wini-mobile-components';
import {ProfileView} from '../../modules/community/pages/Chat';
import {LogoImg} from './Home';
import {DataController} from '../../base/baseController';
import {TypoSkin} from '../../assets/skin/typography';
import {RootScreen} from '../../router/router';
import {useTag} from '../../modules/community/context/TagContext';

const NewsFeed = () => {
  const [isRefresh, setRefresh] = useState(false);
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const {setSelectedTag} = useTag();

  const [tag, setTag] = useState<Array<any>>([]);
  const [loading, setLoading] = useState(true);
  const controller = new DataController('Posts');

  useEffect(() => {
    getData();
  }, []);
  const getData = async () => {
    setLoading(true);

    const resultPost = await controller.group({
      searchRaw: `*`,
      reducers: `APPLY exists(@ListTag) as _exist FILTER @_exist==1 APPLY strlen(@ListTag) as _lentag FILTER @_lentag>0 APPLY split(@ListTag) AS tag GROUPBY 1 @tag REDUCE COUNT 0 AS count SORTBY 2 @count DESC LIMIT 0 8`,
    });

    if (resultPost) {
      setTag(resultPost.data);
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefresh(true);
    await getData();
    setRefresh(false);
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      {/* header */}
      <ListTile
        style={{
          padding: 0,
          paddingBottom: 8,
          paddingHorizontal: 16,
        }}
        isClickLeading
        leading={
          <TouchableOpacity
            onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
            style={{padding: 4}}>
            <LogoImg />
          </TouchableOpacity>
        }
        title={'Cộng đồng'}
        trailing={<ProfileView />}
      />
      {/* content */}
      <ScrollView
        style={{flex: 1}}
        nestedScrollEnabled
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={onRefresh}
            colors={[ColorThemes.light.Primary_Color_Main]}
            tintColor={ColorThemes.light.Primary_Color_Main}
          />
        }>
        {/* show tag list */}
        <View style={{paddingHorizontal: 16}}>
          {tag.length > 0 && (
            <Text
              style={{
                ...TypoSkin.heading6,
                color: ColorThemes.light.Neutral_Text_Color_Title,
              }}>
              Từ khóa phổ biến
            </Text>
          )}
          {tag.length > 0 && (
            <View
              style={{
                width: '100%',
                paddingVertical: 16,
                flexDirection: 'row',
                gap: 8,
                flexWrap: 'wrap',
              }}>
              {tag.map((item, index) => {
                return (
                  <HashTag
                    key={index}
                    title={`${item.tag}`}
                    textStyles={{
                      ...TypoSkin.buttonText6,
                      color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    }}
                    onPress={() => {
                      // Set tag in context
                      setSelectedTag(item);
                      // Navigate to Explore
                      navigation.navigate(RootScreen.CommunityLayout, {
                        screen: 'Khám phá',
                      });
                    }}
                    styles={{
                      borderRadius: 24,
                      backgroundColor:
                        ColorThemes.light.Neutral_Background_Color_Main,
                      paddingHorizontal: 8,
                      paddingVertical: 4,
                      margin: 0,
                      alignItems: 'flex-start',
                      height: undefined,
                    }}
                  />
                );
              })}
            </View>
          )}
        </View>
        <ByNewTrending
          titleList={'Tin tiêu điểm'}
          isRefresh={isRefresh}
          setRefresh={setRefresh}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    width: '100%',
  },
});

export default NewsFeed;
