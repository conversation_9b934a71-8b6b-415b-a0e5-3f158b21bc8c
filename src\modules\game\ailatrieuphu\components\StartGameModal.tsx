import React, {useEffect, useState, useCallback, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
  PixelRatio,
  ActivityIndicator,
  Animated,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../../redux/store/store';
import {resetGame, setCurrentMilestoneId} from '../redux/gameSlice';
import {DataController} from '../../../../base/baseController';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {useSelectorCustomerState} from '../../../../redux/hook/customerHook';
import {getGameConfig} from '../../config/GameConfig';

// Responsive utilities
const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');

// Scale based on screen size
const scale = SCREEN_WIDTH / 375; // 375 is standard width

// Normalize font size for different screen densities
export function normalize(size: number): number {
  const newSize = size * scale;
  if (Platform.OS === 'ios') {
    return Math.round(PixelRatio.roundToNearestPixel(newSize));
  }
  return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 2;
}

// Responsive width and height
export function wp(percentage: number): number {
  return (percentage * SCREEN_WIDTH) / 100;
}

export function hp(percentage: number): number {
  return (percentage * SCREEN_HEIGHT) / 100;
}

// Interface for player data
interface PlayerData {
  id: string;
  name: string;
  score: number;
  date: string;
  time: string;
  avatarUrl?: string;
}

interface StartGameModalProps {
  visible: boolean;
  onClose: () => void;
  competenceId: number;
  gameId: string;
  stageId: any;
  content: string;
  selectedMilestone: {
    number: number;
    levelName?: string;
  } | null;
}

const StartGameModal: React.FC<StartGameModalProps> = ({
  visible,
  onClose,
  selectedMilestone,
  competenceId,
  gameId,
  stageId,
  content,
}) => {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<AppDispatch>();
  const [players, setPlayers] = useState<PlayerData[]>([]);
  const [loading, setLoading] = useState(false);
  const customerCurrent = useSelectorCustomerState().data;
  const gameconfig = getGameConfig(gameId);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  // Fetch top players from HistoryScore table
  const fetchTopPlayers = useCallback(async () => {
    try {
      setLoading(true);
      const historyScoreController = new DataController('HistoryScore');
      const customerController = new DataController('Customer');

      // Get all players for this game - lấy nhiều bản ghi để xử lý gộp theo người dùng
      const response = await historyScoreController.getListSimple({
        query: `@GameId: {${gameId}}`,
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'}, // Sắp xếp theo thời gian tạo mới nhất
        size: 100, // Lấy nhiều bản ghi để đảm bảo có đủ dữ liệu sau khi lọc
      });

      if (response.code === 200 && response.data && response.data.length > 0) {
        // Nhóm dữ liệu theo CustomerId và chỉ lấy bản ghi mới nhất
        const playerMap = new Map();

        // Lặp qua tất cả bản ghi và chỉ giữ lại bản ghi mới nhất của mỗi người dùng
        response.data.forEach((player: any) => {
          // Nếu người dùng chưa có trong map hoặc bản ghi này mới hơn bản ghi đã có
          if (
            !playerMap.has(player.CustomerId) ||
            player.DateCreated > playerMap.get(player.CustomerId).DateCreated
          ) {
            playerMap.set(player.CustomerId, player);
          }
        });

        // Chuyển đổi Map thành mảng và sắp xếp theo thời gian chơi mới nhất
        const uniquePlayers = Array.from(playerMap.values())
          .sort((a, b) => b.DateCreated - a.DateCreated) // Sắp xếp theo thời gian mới nhất
          .slice(0, 3); // Chỉ lấy top 3

        // Lấy danh sách CustomerId để truy vấn thông tin chi tiết
        const customerIds = uniquePlayers.map(player => player.CustomerId);

        // Lấy thông tin chi tiết của người chơi từ bảng Customer
        const customerResponse = await customerController.getListSimple({
          query: `@Id: {${customerIds.join(' | ')}}`,
        });

        // Tạo map để lưu trữ thông tin người dùng theo CustomerId
        const customerMap = new Map();

        if (customerResponse.code === 200 && customerResponse.data) {
          customerResponse.data.forEach((customer: any) => {
            customerMap.set(customer.Id, {
              name: customer.Id === customerCurrent.Id ? 'Tôi' : customer.Name,
              avatarUrl: customer.AvatarUrl,
            });
          });
        }

        // Format dữ liệu kết hợp thông tin từ HistoryScore và Customer
        const formattedPlayers = uniquePlayers.map((player: any) => {
          // Format date and time
          const date = new Date(player.DateCreated);
          const formattedDate = `${date
            .getDate()
            .toString()
            .padStart(2, '0')}/${(date.getMonth() + 1)
            .toString()
            .padStart(2, '0')}/${date.getFullYear()}`;
          const formattedTime = `${date
            .getHours()
            .toString()
            .padStart(2, '0')}:${date
            .getMinutes()
            .toString()
            .padStart(2, '0')}`;

          // Lấy thông tin người dùng từ customerMap
          const customerInfo = customerMap.get(player.CustomerId) || {};

          return {
            id: player.Id,
            name: customerInfo.name || player.Name || 'Player',
            score: player.Score || 0,
            date: formattedDate,
            time: formattedTime,
            avatarUrl: customerInfo.avatarUrl || undefined,
          };
        });

        setPlayers(formattedPlayers);
      } else {
        // If no data, set empty array
        setPlayers([]);
      }
    } catch (error) {
      console.error('Error fetching top players:', error);
      setPlayers([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Animation effect for show/hide modal
  useEffect(() => {
    if (visible) {
      // Show animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Hide animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, fadeAnim, scaleAnim]);

  // Fetch player data when modal becomes visible
  useEffect(() => {
    if (visible && selectedMilestone) {
      fetchTopPlayers();
    }
  }, [visible, selectedMilestone, fetchTopPlayers]);

  // Hàm bắt đầu chơi game
  const startGame = () => {
    dispatch(resetGame()); // Reset game state trước khi bắt đầu

    // Đảm bảo milestone hiện tại đã được đặt
    if (selectedMilestone) {
      dispatch(setCurrentMilestoneId(selectedMilestone.number));
    }

    onClose(); // Đóng modal
    // Điều hướng đến màn hình start
    navigation.push(gameconfig.startGameScreen as never, {
      milestoneId: stageId,
      competenceId,
    });
  };

  if (!visible) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.modalOverlay,
        {
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 10000,
          opacity: fadeAnim,
        },
      ]}>
      {/* Sử dụng một Image duy nhất cho toàn bộ modal */}
      <Animated.View
        style={{
          transform: [{scale: scaleAnim}],
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Image
          source={require('../assets/modal_background_full.png')}
          style={styles.modalContainer}
          resizeMode="contain"
        />

        {/* Overlay cho các phần tử tương tác */}
        <View style={styles.modalInteractiveOverlay}>
          {/* Hiển thị text chặng */}
          <Text style={styles.modalBannerText}>
            Chặng {selectedMilestone?.number}
          </Text>

          {/* Nút đóng - chỉ là TouchableOpacity không có style */}
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}
          />

          {/* Icon hoạt động cộng đồng */}
          <View style={styles.communityIconContainer}>
            <Text style={styles.communityText}>Hoạt động{'\n'}cộng đồng</Text>
          </View>

          {/* Mô tả */}
          <Text style={styles.modalDescription}>{content}</Text>

          {/* Nút START - chỉ là TouchableOpacity không có style */}
          <TouchableOpacity
            style={styles.startButtonTouchable}
            onPress={startGame}
            hitSlop={{top: 10, right: 10, bottom: 10, left: 10}}>
            <Text style={styles.startButtonText}>START</Text>
          </TouchableOpacity>

          {/* Thông tin người chơi */}
          <View style={styles.playerInfoContainer}>
            {loading ? (
              <ActivityIndicator size="large" color="#fff" />
            ) : (
              <>
                {/* Player 1 */}
                <View style={styles.playerInfo}>
                  {players.length > 0 ? (
                    <>
                      <Text style={styles.playerInfoTime}>
                        {players[0].time}
                      </Text>
                      <Text style={styles.playerInfoDate}>
                        {players[0].date}
                      </Text>
                      {players[0].avatarUrl ? (
                        <Image
                          source={{
                            uri: ConfigAPI.getValidLink(players[0].avatarUrl),
                          }}
                          style={styles.playerAvatar}
                          // defaultSource={require('../assets/chuyengia.png')}
                        />
                      ) : (
                        <View style={styles.playerAvatarPlaceholder} />
                      )}
                      <Text
                        style={styles.playerName}
                        numberOfLines={2}
                        ellipsizeMode="tail">
                        {players[0].name}
                      </Text>
                      <Text style={styles.playerScore}>{players[0].score}</Text>
                    </>
                  ) : (
                    <>
                      <Text style={styles.playerInfoTime}>--:--</Text>
                      <Text style={styles.playerInfoDate}>--/--/----</Text>
                      <View style={styles.playerAvatarPlaceholder} />
                      <Text
                        style={styles.playerName}
                        numberOfLines={2}
                        ellipsizeMode="tail">
                        ---
                      </Text>
                      <Text style={styles.playerScore}>0</Text>
                    </>
                  )}
                </View>

                {/* Player 2 */}
                <View style={styles.playerInfo}>
                  {players.length > 1 ? (
                    <>
                      <Text style={styles.playerInfoTime}>
                        {players[1].time}
                      </Text>
                      <Text style={styles.playerInfoDate}>
                        {players[1].date}
                      </Text>
                      {players[1].avatarUrl ? (
                        <Image
                          source={{
                            uri: ConfigAPI.getValidLink(players[1].avatarUrl),
                          }}
                          style={styles.playerAvatar}
                          // defaultSource={require('../assets/chuyengia.png')}
                        />
                      ) : (
                        <View style={styles.playerAvatarPlaceholder} />
                      )}
                      <Text
                        style={styles.playerName}
                        numberOfLines={2}
                        ellipsizeMode="tail">
                        {players[1].name}
                      </Text>
                      <Text style={styles.playerScore}>{players[1].score}</Text>
                    </>
                  ) : (
                    <>
                      <Text style={styles.playerInfoTime}>--:--</Text>
                      <Text style={styles.playerInfoDate}>--/--/----</Text>
                      <View style={styles.playerAvatarPlaceholder} />
                      <Text
                        style={styles.playerName}
                        numberOfLines={2}
                        ellipsizeMode="tail">
                        ---
                      </Text>
                      <Text style={styles.playerScore}>0</Text>
                    </>
                  )}
                </View>

                {/* Player 3 */}
                <View style={styles.playerInfo}>
                  {players.length > 2 ? (
                    <>
                      <Text style={styles.playerInfoTime}>
                        {players[2].time}
                      </Text>
                      <Text style={styles.playerInfoDate}>
                        {players[2].date}
                      </Text>
                      {players[2].avatarUrl ? (
                        <Image
                          source={{
                            uri: ConfigAPI.getValidLink(players[2].avatarUrl),
                          }}
                          style={styles.playerAvatar}
                          // defaultSource={require('../assets/chuyengia.png')}
                        />
                      ) : (
                        <View style={styles.playerAvatarPlaceholder} />
                      )}
                      <Text
                        style={styles.playerName}
                        numberOfLines={2}
                        ellipsizeMode="tail">
                        {players[2].name}
                      </Text>
                      <Text style={styles.playerScore}>{players[2].score}</Text>
                    </>
                  ) : (
                    <>
                      <Text style={styles.playerInfoTime}>--:--</Text>
                      <Text style={styles.playerInfoDate}>--/--/----</Text>
                      <View style={styles.playerAvatarPlaceholder} />
                      <Text
                        style={styles.playerName}
                        numberOfLines={2}
                        ellipsizeMode="tail">
                        ---
                      </Text>
                      <Text style={styles.playerScore}>0</Text>
                    </>
                  )}
                </View>
              </>
            )}
          </View>
        </View>
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: wp(100),
    height: hp(80),
    resizeMode: 'contain',
    position: 'relative',
  },
  // Overlay cho các phần tử tương tác
  modalInteractiveOverlay: {
    position: 'absolute',
    width: wp(100),
    height: hp(85),
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBannerText: {
    color: 'white',
    fontSize: normalize(26),
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: normalize(28),
    position: 'absolute',
    top: hp(7.5),
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: 'transparent',
    textShadowColor: '#C4423D',
    textShadowOffset: {width: 1.5, height: 1.5},
    textShadowRadius: 1,
  },
  closeButton: {
    position: 'absolute',
    top: hp(6.5),
    right: wp(1.5),
    width: wp(14),
    height: wp(14),
    zIndex: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    borderRadius: wp(8),
  },
  modalDescription: {
    fontSize: normalize(20),
    fontWeight: 'bold',
    color: '#FF5A5F',
    textAlign: 'center',
    position: 'absolute',
    // top: hp(30),
    top: hp(32),
  },
  startButtonTouchable: {
    width: 230,
    height: 90,
    top: 55,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor: '#FF4D6D',
  },
  startButtonText: {
    color: 'white',
    fontSize: normalize(36),
    fontWeight: 'bold',
    position: 'absolute',
    //border text
    textShadowColor: '#C4423D',
    textShadowOffset: {width: 1.5, height: 1.5},
    textShadowRadius: 1,
  },
  playerInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    position: 'absolute',
    bottom: hp(6),
    width: wp(68),
    right: wp(6),
    zIndex: 10,
    backgroundColor: 'transparent',
  },
  playerInfo: {
    alignItems: 'center',
    justifyContent: 'flex-start',
    width: wp(20),
    height: hp(18), // Đặt chiều cao cố định cho container
  },
  playerInfoTime: {
    color: 'white',
    fontSize: normalize(12),
    fontWeight: 'bold',
    height: hp(2), // Chiều cao cố định
    marginTop: hp(0.5),
  },
  playerInfoDate: {
    color: 'white',
    fontSize: normalize(10),
    height: hp(2), // Chiều cao cố định
    marginBottom: hp(0.5),
  },
  playerName: {
    color: 'white',
    fontSize: normalize(12),
    fontWeight: 'bold',
    textAlign: 'center',
    height: hp(3.2), // Chiều cao cố định cho tên
    width: wp(18), // Chiều rộng cố định
    marginVertical: hp(0.5),
  },
  playerScore: {
    color: 'white',
    fontSize: normalize(14),
    fontWeight: 'bold',
    height: hp(2), // Chiều cao cố định
    marginTop: hp(0.5),
  },
  // Styles cho icon hoạt động cộng đồng
  communityIconContainer: {
    position: 'absolute',
    left: wp(10),
    bottom: hp(12),
    flexDirection: 'column',
    alignItems: 'center',
  },
  communityText: {
    color: 'white',
    fontSize: normalize(12),
    marginLeft: wp(1),
    textAlign: 'center',
  },
  // Placeholder cho avatar
  playerAvatarPlaceholder: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    backgroundColor: '#FFF',
    marginVertical: hp(0.5),
  },
  // Style cho avatar
  playerAvatar: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    marginVertical: hp(0.5),
    backgroundColor: '#FFF',
  },
});

export default StartGameModal;
