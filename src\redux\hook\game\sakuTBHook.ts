import {useDispatch} from 'react-redux';
import {
  applyGameConfig,
  nextLevel,
  nextQuestion,
  resetState,
  setData,
  startGame,
} from '../../reducers/game/sakuTBReducer';
import {
  GameConfig,
  SakuTBGameState,
} from '../../../modules/game/sakutimban/types/sakuTBTypes';

export const useSakuTBHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: (stateName: keyof SakuTBGameState, value: any) => {
      dispatch(setData({stateName, value}));
    },
    applyGameConfig: (config: GameConfig) => {
      dispatch(applyGameConfig(config));
    },
    startGame: () => {
      dispatch(startGame());
    },
    nextQuestion: () => {
      dispatch(nextQuestion());
    },
    nextLevel: () => {
      dispatch(nextLevel());
    },
    resetState: () => {
      dispatch(resetState());
    },
  };

  return action;
};
