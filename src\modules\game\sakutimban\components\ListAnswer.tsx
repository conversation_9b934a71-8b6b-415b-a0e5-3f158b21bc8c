import React, {useCallback, useMemo, useRef, useEffect, useState} from 'react';

import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Vibration,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
// import { Audio } from "expo-av";
import {useSelector, useDispatch} from 'react-redux';
import {useSakuTBHook} from '../../../../redux/hook/game/sakuTBHook';
import {useGameHook} from '../../../../redux/hook/gameHook';
import {calculateFinalScore} from '../../../../redux/reducers/game/sakuTBReducer';
import ModelDescriptionQuestion from '../../components/ModelDescriptionQuestion';
import ModelDoneLevel from '../../components/ModelDoneLevel';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {Item} from '../types/sakuTBTypes';
import WinnerModal from './WinnerModal';

const maleBirdImages = [
  require('../assets/male_bird_1.png'),
  require('../assets/male_bird_2.png'),
  require('../assets/male_bird_3.png'),
];

const femaleBirdImages = [
  require('../assets/female_bird_1.png'),
  require('../assets/female_bird_2.png'),
  require('../assets/female_bird_3.png'),
];

const birdsImages = [
  require('../assets/birds_1.png'),
  require('../assets/birds_2.png'),
  require('../assets/birds_3.png'),
];

export const playSound = async (_url: string) => {
  // await Audio.setAudioModeAsync({ playsInSilentModeIOS: true });
  // const soundObject = await Audio.Sound.createAsync(
  //   { uri: url },
  //   { shouldPlay: true }
  // );
  // return soundObject.sound;
};

// Tách AnimatedItem ra ngoài để tránh re-create component
const AnimatedItem = React.memo(
  ({
    item,
    onSelected,
    side,
    matchedPairs,
    errorPairs,
    selectedLeft,
    selectedRight,
  }: {
    item: Item;
    onSelected: (item: Item) => void;
    side: string;
    matchedPairs: string[];
    errorPairs: string[];
    selectedLeft: Item | null;
    selectedRight: Item | null;
  }) => {
    const opacity = useSharedValue(1);

    const animatedStyle = useAnimatedStyle(() => ({
      opacity: opacity.value,
      marginVertical: 12,
    }));

    const isMatched = useMemo(
      () => matchedPairs.includes(item.id),
      [matchedPairs, item.id],
    );
    const isError = useMemo(
      () => errorPairs.includes(item.id),
      [errorPairs, item.id],
    );
    const isSelected = useMemo(() => {
      if (side === 'left') return selectedLeft?.id === item.id;
      return selectedRight?.id === item.id;
    }, [side, selectedLeft, selectedRight, item.id]);

    const handlePress = useCallback(() => {
      onSelected(item);
    }, [onSelected, item]);

    const getItem = useMemo(() => {
      if (item.type === 'image') {
        return <Image source={{uri: item.text}} style={styles.itemImage} />;
      } else if (item.type === 'audio') {
        return <Image source={require('../assets/audio.png')} />;
      } else {
        return (
          <Text key={item.id} style={styles.textAnswer}>
            {item.text}
          </Text>
        );
      }
    }, [item]);

    return (
      <Animated.View style={[animatedStyle]}>
        <View>
          <TouchableOpacity
            style={[
              styles.answer,
              isSelected && styles.selectedItem,
              isMatched && styles.matchedItem,
              isError && styles.errorItem,
            ]}
            onPress={handlePress}
            activeOpacity={0.7}>
            {getItem}
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  },
);

const ListAnswer = () => {
  const dispatch = useDispatch();
  const sakuTbHook = useSakuTBHook();
  const gameHook = useGameHook();
  const [isWinQuestion, setIsWinQuestion] = useState(false);
  const [isWinLevel, setIsWinLevel] = useState(false);
  const [showWinnerModal, setShowWinnerModal] = useState(false);
  const {
    listItemsLeft,
    listItemsRight,
    currentQuestion,
    selectedLeft,
    selectedRight,
    matchedPairs,
    errorPairs,
    questionDone,
    listItemsDone,
    currentLevel,
    listQuestions,
    totalQuestion,
    competenceId,
    bonusScore,
    currentQuestionIndex,
    maxLevel,
    currentLives,
    currentScore,
    milestoneId,
  } = useSelector((state: any) => state.SakuTB);

  const [imageIndex, setImageIndex] = useState(0);

  // Debounce ref để tránh multiple clicks
  const isProcessing = useRef(false);
  const nextQuestionTimeout = useRef<NodeJS.Timeout | null>(null);

  // Reset questionCompleted khi chuyển câu hỏi
  useEffect(() => {
    // Clear any pending timeout
    if (nextQuestionTimeout.current) {
      clearTimeout(nextQuestionTimeout.current);
      nextQuestionTimeout.current = null;
    }
    console.log(
      `[ListAnswer] 🔄 Reset for question ${currentQuestionIndex + 1}`,
    );

    const numberRandom = Math.floor(Math.random() * 3); // 0, 1, 2
    setImageIndex(numberRandom);
  }, [currentQuestionIndex]);

  // Xoá item ở list bên trái - Tối ưu với useCallback
  const deleteItemLeft = useCallback(
    (id: string) => {
      sakuTbHook.setData(
        'listItemsLeft',
        listItemsLeft.filter((item: {id: string}) => item.id !== id),
      );
    },
    [listItemsLeft, sakuTbHook],
  );

  // Xoá item ở list bên phải - Tối ưu với useCallback
  const deleteItemRight = useCallback(
    (id: string) => {
      sakuTbHook.setData(
        'listItemsRight',
        listItemsRight.filter((item: {id: string}) => item.id !== id),
      );
    },
    [listItemsRight, sakuTbHook],
  );

  const handleShowWinnerModal = useCallback(() => {
    dispatch(calculateFinalScore());
    setTimeout(() => {
      setShowWinnerModal(true);
      gameHook.setData({stateName: 'isRunTime', value: false}); // Dừng timer
    }, 500); // Delay nhỏ để animation hoàn thành
  }, []);

  const handleNextQuestion = useCallback(() => {
    setIsWinQuestion(false);
    sakuTbHook.nextQuestion();
  }, []);

  const handleNextLevel = useCallback(() => {
    setIsWinLevel(false);
    sakuTbHook.nextLevel();
  }, [sakuTbHook]);

  const checkMatch = useCallback(
    (leftItem: Item, rightItem: Item) => {
      const isCorrect = leftItem.matchId === rightItem.matchId;

      if (isCorrect) {
        // Đúng - Batch multiple updates
        const updatedMatchedPairs = [
          ...matchedPairs,
          leftItem.id,
          rightItem.id,
        ];
        sakuTbHook.setData('matchedPairs', updatedMatchedPairs);

        // Sử dụng setTimeout để tối ưu animation
        setTimeout(() => {
          deleteItemLeft(leftItem.id);
          deleteItemRight(rightItem.id);

          sakuTbHook.setData('listItemsDone', [
            ...listItemsDone,
            {listItems: [leftItem, rightItem]},
          ]);

          // Kiểm tra xem có phải cặp cuối cùng không
          const newLeftItems = listItemsLeft.filter(
            (item: Item) => item.id !== leftItem.id,
          );
          const newRightItems = listItemsRight.filter(
            (item: Item) => item.id !== rightItem.id,
          );
          if (
            updatedMatchedPairs.length ===
              [...currentQuestion.leftItems, ...currentQuestion.rightItems]
                .length &&
            newLeftItems.length === 0 &&
            newRightItems.length === 0
          ) {
            // Clear any existing timeout
            if (nextQuestionTimeout.current) {
              clearTimeout(nextQuestionTimeout.current);
            }
            nextQuestionTimeout.current = setTimeout(() => {
              if (questionDone + 1 < totalQuestion) {
                setIsWinQuestion(true);
              } else {
                if (currentLevel >= maxLevel) {
                  return handleShowWinnerModal();
                }
                setIsWinLevel(true);
              }
              nextQuestionTimeout.current = null;
            }, 0);
          }

          isProcessing.current = false;
        }, 300); // Giảm delay từ 500ms xuống 300ms
      } else {
        // Sai - Rung thiết bị
        Vibration.vibrate([0, 500, 200, 500]);

        sakuTbHook.setData('errorPairs', [
          ...errorPairs,
          leftItem.id,
          rightItem.id,
        ]);
        sakuTbHook.setData('currentLives', currentLives - 1);
      }

      setTimeout(() => {
        sakuTbHook.setData('selectedLeft', null);
        sakuTbHook.setData('selectedRight', null);
        sakuTbHook.setData('errorPairs', []);
        if (!isCorrect) {
          isProcessing.current = false;
        }
      }, 300); // Giảm delay
    },
    [
      matchedPairs,
      errorPairs,
      listItemsDone,
      listItemsLeft,
      listItemsRight,
      currentLives,
      sakuTbHook,
      gameHook,
      deleteItemLeft,
      deleteItemRight,
      dispatch,
      currentQuestionIndex,
      questionDone,
      totalQuestion,
      listQuestions.length,
    ],
  );

  // Chọn đáp án bên trái
  const handleLeftSelect = useCallback(
    (item: Item) => {
      if (isProcessing.current || matchedPairs.includes(item.id)) {
        return;
      }

      isProcessing.current = true;
      sakuTbHook.setData('selectedLeft', item);

      if (selectedRight) {
        checkMatch(item, selectedRight);
      } else {
        isProcessing.current = false;
      }
    },
    [matchedPairs, selectedRight, sakuTbHook, checkMatch],
  );

  // Chọn đáp án bên phải
  const handleRightSelect = useCallback(
    (item: Item) => {
      if (isProcessing.current || matchedPairs.includes(item.id)) {
        return;
      }

      isProcessing.current = true;
      if (item.type === 'audio') {
        playSound(item.text);
      }

      sakuTbHook.setData('selectedRight', item);

      if (selectedLeft) {
        checkMatch(selectedLeft, item);
      } else {
        isProcessing.current = false;
      }
    },
    [matchedPairs, selectedLeft, sakuTbHook, checkMatch],
  );

  // Hiển thị item đã đúng
  const getItemDone = (item: Item) => {
    if (item.type === 'image') {
      return (
        <Image
          style={{width: 65, height: 65}}
          source={{uri: item.text}}></Image>
      );
    } else if (item.type === 'audio') {
      return <Image source={require('../assets/audio.png')}></Image>;
    } else {
      return (
        <Text key={item.id} style={styles.textAnswer}>
          {item.text}
        </Text>
      );
    }
  };

  return (
    <View>
      <View
        style={{
          width: '100%',
          flexDirection: 'row',
          justifyContent: 'space-around',
        }}>
        <View style={styles.container}>
          <Image
            style={{height: 80}}
            source={femaleBirdImages[imageIndex]}></Image>
          <ScrollView>
            {listItemsLeft.map((item: Item) => (
              <AnimatedItem
                item={item}
                key={item.id}
                onSelected={handleLeftSelect}
                side="left"
                matchedPairs={matchedPairs}
                errorPairs={errorPairs}
                selectedLeft={selectedLeft}
                selectedRight={selectedRight}
              />
            ))}
          </ScrollView>
        </View>
        <View style={styles.container}>
          <Image
            style={{height: 80}}
            source={maleBirdImages[imageIndex]}></Image>
          <ScrollView>
            {listItemsRight.map((item: Item) => (
              <AnimatedItem
                item={item}
                key={item.id}
                onSelected={handleRightSelect}
                side="right"
                matchedPairs={matchedPairs}
                errorPairs={errorPairs}
                selectedLeft={selectedLeft}
                selectedRight={selectedRight}
              />
            ))}
          </ScrollView>
        </View>
      </View>
      <View>
        {listItemsDone.map((item: any, index: number) => (
          <View style={styles.doneItem} key={index}>
            <View style={{width: 70}}>
              <Image source={birdsImages[imageIndex]}></Image>
            </View>
            {item.listItems.map((item: Item) => (
              <View key={item.id} style={{flex: 1, marginLeft: 12}}>
                {getItemDone(item)}
              </View>
            ))}
          </View>
        ))}
      </View>
      <ModelDescriptionQuestion
        visible={isWinQuestion}
        onNext={handleNextQuestion}
        message={''}
      />
      <ModelDoneLevel
        visible={isWinLevel}
        message={`Bạn đã vượt qua cấp ${currentLevel}`}
        onNextLevel={handleNextLevel}
        gameId={ConfigAPI.gameSakuTC}
        competenceId={competenceId}
        totalScore={bonusScore}
      />
      <WinnerModal
        visible={showWinnerModal}
        onClose={() => setShowWinnerModal(false)}
        restartGame={() => {}}
        bonusScore={currentScore}
        bonusRanking={currentScore}
        milestoneId={milestoneId}
        competenceId={competenceId}
        gameId={ConfigAPI.gameSakuTB}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '45%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  answer: {
    width: 140,
    minHeight: 65,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FCF8E8',
    borderRadius: 12,
  },
  textAnswer: {
    padding: 4,
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#112164',
  },
  itemImage: {
    width: 50,
    height: 50,
  },
  selectedItem: {
    backgroundColor: '#2196F3',
    borderColor: '#0d8aee',
  },
  matchedItem: {
    backgroundColor: '#4CAF50',
    borderColor: '#3d8b40',
  },
  errorItem: {
    backgroundColor: '#FF5722',
    borderColor: '#3d8b40',
  },
  doneItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 12,
  },
});

export default ListAnswer;
