import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ScrollView, FlatList} from 'react-native-gesture-handler';
import {
  Winicon,
  AppButton,
  SkeletonImage,
  ListTile,
  FDialog,
  FLoading,
  FBottomSheet,
  showBottomSheet,
  hideBottomSheet,
  ComponentStatus,
  showDialog,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {navigate, navigateBack, RootScreen} from '../../../router/router';
import ConfigAPI from '../../../Config/ConfigAPI';
import CommentsListNews from '../../customer/listview/commentsNews';
import {useRoute} from '@react-navigation/native';
import {onShare} from '../../../features/share';
import {useForm} from 'react-hook-form';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {TextFieldForm} from '../news/form/component-form';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import store, {AppDispatch} from '../../../redux/store/store';
import {useDispatch} from 'react-redux';
import {SafeAreaView} from 'react-native-safe-area-context';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {postCommentsActions} from '../reducers/postCommentsReducer';
import RenderHTML from 'react-native-render-html';
import FastImage from 'react-native-fast-image';
import {myFeedActions} from '../reducers/MyFeedReducer';
import {GroupPostsActions} from '../reducers/groupPostsReducer';
import {DataController} from '../../../base/baseController';
import EmptyPage from '../../../Screen/emptyPage';
import ClickableImage from '../../exam/components/ClickableImage';
import YouTubePlayer from '../../../utils/YouTubeWebViewPlayer';
import {extractVideoId} from '../card/DefaultPostModule/utils';
import ContentBackground from '../card/DefaultPostModule/ContentBackground';
import {useTranslation} from 'react-i18next';
import {HTMLContent} from '../card/DefaultPostModule';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {StorageContanst} from '../../../Config/Contanst';
import {randomGID, Ultis} from '../../../utils/Utils';

export default function NewsDetail() {
  const route = useRoute<any>();
  const {item} = route.params;
  const [data, setItem] = useState<any>({
    ...item,
    Likes: item.Likes || [],
    IsLike: item.IsLike || false,
    Comments: item.Comments || [],
  });
  const dispatch: AppDispatch = useDispatch();
  const scrollViewRef = useRef<ScrollView>(null);
  const bottomSheetRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const {t} = useTranslation();
  const user = useSelectorCustomerState().data;
  const cusId = user?.Id;
  const dialogRef = useRef<any>(null);

  React.useEffect(() => {
    getLikes();
  }, [data?.Id]);

  const getLikes = async () => {
    const likeController = new DataController('Likes');

    // Get likes data
    const likesResponse = await likeController.getListSimple({
      page: 1,
      size: 10000,
      query: `@NewsId:{${data?.Id}}`,
    });

    // get comments
    const commentController = new DataController('Comments');
    const commentResponse = await commentController.getListSimple({
      page: 1,
      size: 10000,
      query: `@NewsId:{${data?.Id}}`,
    });

    // Get customer data for likes
    const customerResponse = await likeController.getPatternList({
      page: 1,
      size: 10000,
      query: `@NewsId:{${data?.Id}}`,
      pattern: {
        CustomerId: ['Id', 'Name', 'AvatarUrl'],
      },
    });

    if (likesResponse?.code === 200 && customerResponse?.code === 200) {
      const likes = likesResponse.data || [];
      const customers = customerResponse.Customer || [];
      const comments = commentResponse.data || [];

      setItem((prevData: any) => ({
        ...prevData,
        Likes: likes,
        IsLike: cusId
          ? likes.some((like: any) => like.CustomerId === cusId)
          : false,
        CustomerLiked: customers,
        Comments: comments,
      }));
    }
  };

  // Xử lý danh sách ảnh
  const imageList = React.useMemo(() => {
    if (!data?.Img) return [];

    // Nếu Img chứa dấu phẩy, tách thành mảng các ảnh
    if (data.Img.includes(',')) {
      return data.Img.split(',')
        .filter((img: string) => img && img.trim() !== '')
        .map((img: string) => ConfigAPI.getValidLink(img.trim()));
    }

    // Nếu chỉ có một ảnh
    return [ConfigAPI.getValidLink(data.Img)];
  }, [data?.Img]);

  // Cấu hình FastImage
  const fastImageProps = React.useMemo(
    () => ({
      priority: FastImage.priority.normal,
      cache: FastImage.cacheControl.immutable,
      resizeMode: FastImage.resizeMode.cover,
    }),
    [],
  );

  const methods = useForm<any>({
    shouldFocusError: false,
  });

  // Handle like functionality similar to byTrending
  const handleLike = async () => {
    if (cusId) {
      const likeController = new DataController('Likes');
      // add like in api
      if (data.IsLike === true) {
        const result = await likeController.getListSimple({
          query: `@CustomerId: {${cusId}} @NewsId:{${data.Id}}`,
        });
        if (result.data?.length > 0) {
          const unlike = await likeController.delete([result.data[0].Id]);
          if (unlike.code === 200) {
            setItem((prevData: any) => ({
              ...prevData,
              IsLike: false,
              Likes:
                data.Likes?.filter((a: any) => a.CustomerId !== cusId) || [],
            }));
          }
        }
      } else {
        const likeData = {
          Id: randomGID(),
          CustomerId: cusId,
          NewsId: data.Id,
          Type: 1,
          DateCreated: new Date().getTime(),
        };
        const result = await likeController.add([likeData]);
        if (result.code === 200) {
          setItem((prevData: any) => ({
            ...prevData,
            IsLike: true,
            Likes: [...(data.Likes || []), likeData],
          }));
        }
      }
    } else {
      dialogCheckAcc(dialogRef);
    }
  };

  const handleReply = useCallback(
    async (dataComment?: any) => {
      if (dataComment) {
        methods.setValue('Comment', undefined);
        methods.setValue('Comment', `@${dataComment.relativeUser.title} `);
        methods.setValue('CommentId', `${dataComment.Id}`);
        methods.setValue('UserComment', `${dataComment.relativeUser.title}`);
        // Focus the comment input field
      } else {
        methods.setValue('Comment', undefined);
        methods.setValue('CommentId', undefined);
        methods.setValue('UserComment', undefined);
      }
    },
    [methods],
  );

  const handleAddComment = async () => {
    var token = await getDataToAsyncStorage(StorageContanst.accessToken);
    if (token) {
      if (methods.getValues().Comment) {
        // Update comment count in local state
        setItem((prevData: any) => ({
          ...prevData,
          Comment: (prevData.Comment || 0) + 1,
        }));

        if (
          methods.getValues().CommentId === undefined ||
          methods.getValues().CommentId === ''
        ) {
          scrollViewRef.current?.scrollToEnd({
            animated: true,
          });
        }

        methods.setValue('Comment', '');
        methods.setValue('CommentId', '');
        methods.setValue('UserComment', '');

        // add comment in api
        const comment = new DataController('Comments');
        const result = await comment.add([
          {
            Id: randomGID(),
            CustomerId: cusId,
            NewsId: data.Id,
            Content: methods.getValues().Comment,
            ParentId: methods.getValues().CommentId,
            DateCreated: new Date().getTime(),
            Likes: 0,
          },
        ]);
        if (result.code === 200) {
          getLikes();
        }
      }
    } else {
      dialogCheckAcc(dialogRef);
    }
  };

  const renderContentBackground = useMemo(() => {
    if (!data?.PostBackgroundM) return null;
    return (
      <View style={styles.contentContainer}>
        <ContentBackground
          text={data?.Content || ''}
          background={data?.PostBackgroundM}
        />
      </View>
    );
  }, [data?.Content, data?.PostBackgroundM]);

  const renderContent = useMemo(() => {
    if (data?.PostBackgroundM) return renderContentBackground;
    return (
      <View style={styles.contentContainer}>
        {/* Text Content */}
        {data?.Content && !data?.PostBackgroundM ? (
          <HTMLContent
            content={data?.Content || ''}
            contentWidth={Dimensions.get('screen').width}
            isContentExpanded={true}
            onToggleExpand={() => {}}
          />
        ) : null}

        {/* Video Content */}
        {data?.LinkVideo ? (
          <View style={styles.videoContainer}>
            <YouTubePlayer
              videoId={extractVideoId(data?.LinkVideo) || ''}
              height={200}
              width={'100%'}
              play={false}
              useNativeControls={true}
              playerParams={{
                modestbranding: true,
              }}
            />
          </View>
        ) : null}

        {/* Image Content */}
        {imageList.length > 0 ? (
          imageList.length === 1 ? (
            // Trường hợp chỉ có 1 ảnh - giữ nguyên hiển thị như cũ
            <View style={styles.singleImageContainer}>
              <ClickableImage
                source={{
                  uri:
                    imageList[0] ||
                    'https://placehold.co/234/FFFFFF/000000/png',
                  ...fastImageProps,
                }}
                style={styles.singleImage}
              />
            </View>
          ) : (
            // Trường hợp có nhiều ảnh - hiển thị theo chiều dọc
            <View>
              <FlatList
                data={imageList}
                scrollEnabled={false}
                renderItem={({item: imageUri}) => (
                  <View style={styles.multipleImageItem}>
                    <ClickableImage
                      source={{
                        uri: imageUri,
                        ...fastImageProps,
                      }}
                      style={styles.multipleImage}
                    />
                  </View>
                )}
                keyExtractor={(item, index) => index.toString()}
              />
            </View>
          )
        ) : null}
      </View>
    );
  }, [data?.LinkVideo, data?.Content, data?.PostBackgroundM, imageList]);

  return (
    <SafeAreaView edges={['top']} style={styles.safeAreaContainer}>
      <FDialog ref={dialogRef} />
      <FBottomSheet ref={bottomSheetRef} />
      <FLoading
        visible={isLoading}
        avt={require('../../../assets/appstore.png')}
      />
      <ListTile
        style={styles.headerListTile}
        isClickLeading
        onPress={() => {
          if (!data?.relativeUser?.title && !data?.relativeUser?.image) return;
          if (data.GroupId) {
            navigate(RootScreen.GroupIndex, {Id: item.GroupId});
          } else {
            navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
          }
        }}
        leading={
          <View style={styles.headerLeading}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigateBack()}>
              <Winicon src="outline/arrows/left-arrow" size={20} />
            </TouchableOpacity>
            {!data?.relativeUser?.title &&
            !data?.relativeUser?.image ? null : data?.relativeUser?.image ? (
              <FastImage
                key={data?.relativeUser?.image}
                source={{
                  uri: data?.relativeUser?.image.includes('https')
                    ? data?.relativeUser?.image
                    : ConfigAPI.getValidLink(data?.relativeUser?.image),
                }}
                style={styles.userAvatar}
              />
            ) : (
              <View style={styles.userInitial}>
                <Text style={styles.userInitialText}>
                  {data?.relativeUser?.title
                    ? data?.relativeUser?.title.charAt(0).toUpperCase()
                    : ''}
                </Text>
              </View>
            )}
          </View>
        }
        title={
          <Text style={styles.userName} numberOfLines={1}>
            {data?.relativeUser?.title ?? ''}
          </Text>
        }
        subtitle={
          <Text
            onPress={() => {
              if (!data?.relativeUser?.title && !data?.relativeUser?.image)
                return;
              navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
            }}
            style={styles.userSubtitle}
            numberOfLines={1}>
            {data?.relativeUser?.subtitle ?? ''}
          </Text>
        }
        subTitleStyle={styles.userSubtitle}
        trailing={
          <View style={styles.headerActions}>
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              onPress={() => {
                showBottomSheet({
                  ref: bottomSheetRef,
                  title: 'Actions',
                  suffixAction: <View />,
                  prefixAction: (
                    <TouchableOpacity
                      onPress={() => hideBottomSheet(bottomSheetRef)}
                      style={styles.closeBottomSheetButton}>
                      <Winicon
                        src="outline/layout/xmark"
                        size={20}
                        color={ColorThemes.light.Neutral_Text_Color_Body}
                      />
                    </TouchableOpacity>
                  ),
                  children: (
                    <View style={styles.bottomSheetContent}>
                      {data.CustomerId !== user.Id && (
                        <ListTile
                          onPress={() => {
                            hideBottomSheet(bottomSheetRef);

                            showDialog({
                              ref: dialogRef,
                              status: ComponentStatus.WARNING,
                              title: 'Báo cáo bài đăng này?',
                              content: (
                                <View style={{height: 140, gap: 8}}>
                                  <Text
                                    style={{
                                      ...TypoSkin.body3,
                                      color:
                                        ColorThemes.light.neutral_main_color,
                                      textAlign: 'center',
                                    }}>
                                    Báo cáo vi phạm sẽ giúp chúng tôi cải thiện
                                    dịch vụ. Xin cảm ơn.
                                  </Text>
                                  <TextFieldForm
                                    control={methods.control}
                                    name="Content"
                                    placeholder="Nhập lý do báo cáo"
                                    returnKeyType="done"
                                    errors={methods.formState.errors}
                                    multiline={true}
                                    maxLength={100}
                                    textFieldStyle={{
                                      height: 90,
                                      paddingTop: 8,
                                      paddingLeft: 8,
                                      backgroundColor:
                                        ColorThemes.light.transparent,
                                    }}
                                    register={methods.register}
                                    onBlur={async (ev: string) => {
                                      var code = ev.trim();
                                    }}
                                  />
                                </View>
                              ),

                              onSubmit: async () => {
                                dispatch(
                                  newsFeedActions.reportPost({
                                    ...item,
                                    ContentReport: methods.getValues().Content,
                                  }),
                                );
                                methods.setValue('Content', undefined);
                              },
                            });
                          }}
                          title={t('community.reportPost')}
                          titleStyle={{...TypoSkin.body3}}
                        />
                      )}
                      {data.CustomerId === user.Id && (
                        <ListTile
                          onPress={() => {
                            hideBottomSheet(bottomSheetRef);

                            navigate(RootScreen.createPost, {
                              editPost: data,
                              groupId: null,
                            });
                          }}
                          title={'Edit post'}
                          titleStyle={{...TypoSkin.body3}}
                        />
                      )}
                      {data.CustomerId === user.Id && (
                        <ListTile
                          onPress={() => {
                            hideBottomSheet(bottomSheetRef);
                            showDialog({
                              ref: dialogRef,
                              status: ComponentStatus.WARNING,
                              title: 'Bạn chắc chắn muốn xóa bài đăng này?',
                              onSubmit: async () => {
                                dispatch(newsFeedActions.deletePost(data));
                              },
                            });
                          }}
                          title={'Delete post'}
                          titleStyle={{...TypoSkin.body3}}
                        />
                      )}
                    </View>
                  ),
                });
              }}
              containerStyle={styles.actionButton}
              title={
                <Winicon
                  src={'fill/user interface/menu-dots'}
                  size={14}
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                />
              }
            />
          </View>
        }
      />
      {/* contents */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'height' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 0}
        // Add these props for smoother animation
        contentContainerStyle={styles.keyboardAvoidingContentContainer}
        style={styles.keyboardAvoidingContainer}>
        <ScrollView ref={scrollViewRef} style={styles.scrollView}>
          {/* Regular Content (video, text, images) */}
          {renderContent}
          {/* actions */}
          <View style={styles.actionsContainer}>
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              containerStyle={styles.actionButtonContainer}
              title={
                <Text style={styles.actionButtonText}>
                  {data?.Likes?.length ?? 0}
                </Text>
              }
              textColor={
                data?.IsLike === true
                  ? ColorThemes.light.Error_Color_Main
                  : ColorThemes.light.Neutral_Text_Color_Subtitle
              }
              onPress={handleLike}
              prefixIconSize={12}
              prefixIcon={
                data?.IsLike === true
                  ? 'fill/emoticons/heart'
                  : 'outline/emoticons/heart'
              }
            />
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              containerStyle={styles.actionButtonContainer}
              prefixIcon={'outline/user interface/b-comment'}
              prefixIconSize={12}
              textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
              title={
                <Text style={styles.actionButtonText}>
                  {data?.Comment ?? 0}
                </Text>
              }
              onPress={async () => {
                var token = await getDataToAsyncStorage(
                  StorageContanst.accessToken,
                );
                if (token) {
                  // Scroll to comments section
                  scrollViewRef.current?.scrollToEnd({
                    animated: true,
                  });
                } else {
                  dialogCheckAcc(dialogRef);
                }
              }}
            />
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              containerStyle={styles.actionButtonContainer}
              onPress={async () => {
                onShare({
                  content: `${ConfigAPI.urlWeb}/news?id=${data?.Id}`,
                });
              }}
              prefixIcon={'fill/arrows/social-sharing'}
              prefixIconSize={12}
              textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
            />
          </View>
          {/* likes */}
          {data?.CustomerLiked?.length > 0 ? (
            <TouchableOpacity
              onPress={() => {
                showBottomSheet({
                  ref: bottomSheetRef,
                  enableDismiss: true,
                  title: 'Likes',
                  children: (
                    <View style={styles.likesBottomSheetContent}>
                      <Pressable style={styles.likesBottomSheetPressable}>
                        <FlatList
                          data={data?.CustomerLiked}
                          nestedScrollEnabled
                          style={styles.likesList}
                          keyExtractor={(item, index) => index.toString()}
                          ListEmptyComponent={() => {
                            return <EmptyPage title="Không có dữ liệu" />;
                          }}
                          ListFooterComponent={() => {
                            return <View style={styles.likesListFooter} />;
                          }}
                          renderItem={({item}) => {
                            return (
                              <ListTile
                                key={item?.Id}
                                onPress={() => {
                                  hideBottomSheet(bottomSheetRef);
                                  navigate(RootScreen.ProfileCommunity, {
                                    Id: item?.Id,
                                  });
                                }}
                                listtileStyle={styles.likesListTile}
                                leading={
                                  <FastImage
                                    source={
                                      item?.AvatarUrl
                                        ? {
                                            uri: ConfigAPI.getValidLink(
                                              item?.AvatarUrl,
                                            ),
                                          }
                                        : require('../../../assets/appstore.png')
                                    }
                                    style={styles.likesUserAvatar}
                                  />
                                }
                                title={
                                  <Text
                                    style={styles.likesUserName}
                                    numberOfLines={1}>
                                    {item?.Name}
                                  </Text>
                                }
                              />
                            );
                          }}
                        />
                      </Pressable>
                    </View>
                  ),
                });
              }}
              style={styles.likesButton}>
              <Text style={styles.likesText}>
                {data?.CustomerLiked?.length == 1
                  ? `${data?.CustomerLiked[0].Name} đã thích bài viết này`
                  : `${data?.CustomerLiked?.length} người đã thích bài viết này`}
              </Text>
            </TouchableOpacity>
          ) : null}
          {/* comments */}
          <CommentsListNews postId={data?.Id} onReply={handleReply} />
        </ScrollView>
      </KeyboardAvoidingView>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
        style={styles.commentInputContainer}>
        <View style={styles.commentInputWrapper}>
          {methods.watch('CommentId') ? (
            <View style={styles.replyToContainer}>
              <Text style={styles.replyToText}>
                Reply to {methods.getValues().UserComment ?? ''}
              </Text>
              <Text
                onPress={() => {
                  methods.setValue('CommentId', undefined);
                  methods.setValue('Comment', undefined);
                  methods.setValue('UserComment', undefined);
                }}
                style={styles.cancelReplyText}>
                - Hủy
              </Text>
            </View>
          ) : null}
          <View style={styles.commentInputRow}>
            <TextFieldForm
              textFieldStyle={styles.commentTextField}
              style={styles.commentTextFieldContainer}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              placeholder="Viết bình luận của bạn"
              name="Comment"
            />
            <AppButton
              prefixIcon={'fill/user interface/send-message'}
              prefixIconSize={24}
              backgroundColor={ColorThemes.light.transparent}
              borderColor="transparent"
              containerStyle={styles.sendButton}
              onPress={handleAddComment}
              textColor={ColorThemes.light.Primary_Color_Main}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderBottomWidth: 0,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginBottom: 8,
    height: 56,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    paddingHorizontal: 17,
    borderRadius: 20,
    borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
    borderWidth: 1,
  },
  activeTab: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderWidth: 0,
  },
  tabText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginLeft: 4,
  },
  activeTabText: {
    color: '#333',
    fontWeight: 'bold',
  },
  postContainer: {
    backgroundColor: '#fff',
    marginBottom: 8,
  },
  defaultPostContainer: {
    paddingHorizontal: 0,
    marginBottom: 0,
  },
  postHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bookmarkButton: {
    marginRight: 16,
  },
  postFooter: {
    flexDirection: 'row',
    paddingTop: 16,
    alignItems: 'center',
    gap: 8,
  },
  footerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  footerButtonText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  safeAreaContainer: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  headerListTile: {
    paddingHorizontal: 16,
    padding: 0,
    paddingBottom: 16,
  },
  headerLeading: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    paddingRight: 16,
    paddingVertical: 8,
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 50,
    backgroundColor: '#f0f0f0',
  },
  userInitial: {
    width: 32,
    height: 32,
    borderRadius: 50,
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userInitialText: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  userName: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  userSubtitle: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  actionButton: {
    borderRadius: 100,
    padding: 6,
    height: 24,
    width: 24,
  },
  closeBottomSheetButton: {
    padding: 6,
    alignItems: 'center',
  },
  bottomSheetContent: {
    gap: 8,
    height: Dimensions.get('window').height / 4,
    width: '100%',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  keyboardAvoidingContainer: {
    flex: 1,
  },
  keyboardAvoidingContentContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    gap: 8,
  },
  videoContainer: {
    marginBottom: 8,
  },
  singleImageContainer: {
    height: 234,
    width: '100%',
    marginTop: 4,
  },
  singleImage: {
    width: '100%',
    height: 234,
    borderRadius: 8,
  },
  multipleImageItem: {
    marginTop: 4,
  },
  multipleImage: {
    width: '100%',
    height: 234,
    borderRadius: 8,
  },
  actionsContainer: {
    flexDirection: 'row',
    paddingTop: 16,
    paddingHorizontal: 16,
    alignItems: 'center',
    gap: 8,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
    paddingBottom: 16,
  },
  actionButtonContainer: {
    padding: 4,
    height: 24,
    paddingVertical: 0,
    paddingHorizontal: 8,
  },
  actionButtonText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  likesButton: {
    padding: 16,
  },
  likesText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  likesBottomSheetContent: {
    height: Dimensions.get('window').height / 1.66,
    width: '100%',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  likesBottomSheetPressable: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  likesList: {
    height: '100%',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  likesListFooter: {
    height: 32,
  },
  likesListTile: {
    gap: 8,
  },
  likesUserAvatar: {
    width: 48,
    height: 48,
    borderRadius: 50,
    backgroundColor: '#f0f0f0',
  },
  likesUserName: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  commentInputContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  commentInputWrapper: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    paddingHorizontal: 16,
    marginBottom: 32,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  replyToContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  replyToText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  cancelReplyText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  commentInputRow: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  commentTextField: {
    padding: 16,
    height: 40,
    paddingVertical: 0,
  },
  commentTextFieldContainer: {
    flex: 1,
  },
  sendButton: {
    paddingHorizontal: 12,
    height: 45,
  },
});
