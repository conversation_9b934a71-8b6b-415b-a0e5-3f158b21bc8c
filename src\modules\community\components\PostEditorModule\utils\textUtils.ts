import ConfigAPI from '../../../../../Config/ConfigAPI';
import {TextSegment} from '../types';


// Hàm chuyển đổi HTML thành segments đơn giản
export const htmlToSegments = (html: string): TextSegment[] => {
  if (!html) return [{text: ''}];

  // Loại bỏ tất cả các thẻ HTML và chỉ giữ lại text
  let cleanText = html
    .replace(/<[^>]+>/g, '') // Loại bỏ tất cả thẻ HTML
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#039;/g, "'")
    .replace(/\n/g, ' ')
    .trim();

  // Nếu không có text, trả về segment trống
  if (!cleanText) {
    return [{text: ''}];
  }

  return [{text: cleanText}];
};

// Hàm hợp nhất các segment liền kề có cùng định dạng
export const mergeAdjacentSegments = (
  segments: TextSegment[],
  preserveMentions: boolean = false,
): TextSegment[] => {
  if (segments.length <= 1) return segments;

  const result: TextSegment[] = [segments[0]];
  for (let i = 1; i < segments.length; i++) {
    const current = segments[i];
    const previous = result[result.length - 1];

    // Không hợp nhất nếu một trong hai segment là mention hoặc hashtag
    const shouldNotMerge = preserveMentions && (
      previous.isMention || current.isMention ||
      previous.isHashtag || current.isHashtag
    );

    // Kiểm tra xem có thể merge không - chỉ merge khi tất cả thuộc tính giống nhau
    const canMerge = !shouldNotMerge &&
      previous.isMention === current.isMention &&
      previous.isHashtag === current.isHashtag &&
      previous.mentionUserId === current.mentionUserId &&
      // Đảm bảo cả hai đều không phải mention hoặc hashtag
      !previous.isMention && !current.isMention &&
      !previous.isHashtag && !current.isHashtag;

    if (canMerge) {
      // Nếu định dạng giống nhau và không phải mention/hashtag, hợp nhất
      previous.text += current.text;
    } else {
      // Nếu định dạng khác nhau hoặc là mention/hashtag, thêm segment mới
      result.push(current);
    }
  }
  return result;
};

// Lấy full text từ segments
export const getFullText = (segments: TextSegment[]): string => {
  return segments.reduce((text, segment) => text + segment.text, '');
};

// Chuyển đổi segments thành HTML với mentions
export const segmentsToHTMLWithMentions = (segments: TextSegment[]): string => {
  return segments.map(segment => {
    if (segment.isMention && segment.mentionUserId) {
      return `<a href="${ConfigAPI.urlWeb}/profile-social?id=${segment.mentionUserId}" target=" blank" class="people-tag-link">${segment.text}</a>`;
    }
    if (segment.isHashtag && segment.text.startsWith('#')) {
      const hashtag = segment.text;
      return `<a href="https://eschool.wini.vn/search?q=${hashtag}" target="_blank" class="search-tag-link">${hashtag}</a>`;
    }
    return segment.text;
  }).join('');
};

// Lấy danh sách hashtags từ segments
export const extractHashtags = (segments: TextSegment[]): string => {
  const hashtags = segments
    .filter(segment => segment.isHashtag && segment.text.startsWith('#'))
    .map(segment => segment.text.substring(1)) // Bỏ dấu #
    .filter(tag => tag.length > 0);

  return hashtags.join(', ');
};

// Lấy video links từ text
export const extractVideoLinks = (text: string): string => {
  const videoRegex = /https?:\/\/[^\s]+/g;
  const matches = text.match(videoRegex);

  if (!matches) return '';

  // Lọc chỉ lấy video links
  const videoLinks = matches.filter(link => {
    const videoPatterns = [
      /youtube\.com/,
      /youtu\.be/,
      /vimeo\.com/,
      /dailymotion\.com/,
      /facebook\.com/,
      /instagram\.com/,
      /tiktok\.com/,
      /twitter\.com/,
      /x\.com/,
    ];
    // hoặc link có đuôi mp4
    videoPatterns.push(/\.mp4$/);

    return videoPatterns.some(pattern => pattern.test(link));
  });

  return videoLinks.length > 0 ? videoLinks[0] : ''; // Chỉ lấy link đầu tiên
};

export const PostStatus = {
  public: 1,
  private: 2,
};
